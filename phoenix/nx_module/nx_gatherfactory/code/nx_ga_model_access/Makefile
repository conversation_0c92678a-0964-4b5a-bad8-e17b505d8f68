#define the path of search
VPATH = -I./ -I../nx_ga_common -I../../../../nx_common -I../../../../platform_include/plm_common \
		-I../../../../platform_include/plm_commun -I../../../../platform_include/plm_dbm
LABLE = 1.0.15.2

#cflags definition
_D=Y
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	MYLIB_PATH  = -L../../../../nx_lib/release/
	PLATFORM_LIB_PATH  = -L../../../../platform_lib/release/
	OUT_PATH = ../../../../nx_bin/release/ga/
	OBJ_PATH = ./release/
	OBJ_NAME = nx_ga_model_access.so-$(LABLE)
else
	RUN_FLAGS = -g
	MYLIB_PATH  = -L../../../../nx_lib/debug/
	PLATFORM_LIB_PATH  = -L../../../../platform_lib/debug/
	OUT_PATH = ../../../../nx_bin/debug/ga/
	OBJ_PATH = ./debug/
	OBJ_NAME = nx_ga_model_access.so
endif

GA_TYPE=Z2000_GA
ifeq ($(Z2800_GA),Y)
	GA_TYPE=Z2800_GA
endif

LIBS =  $(MYLIB_PATH) -L$(OUT_PATH) -lpthread -lplm_db -lsylib -ldl -rdynamic -lrt $(PLATFORM_LIB_PATH)

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__ -D$(GA_TYPE)

#object file definition
OBJS = $(OBJ_PATH)GetFactoryConfig.o $(OBJ_PATH)nx_ga_model_access_Interface.o

nx_ga_model_access.so : $(OBJS) mksylibdir
	g++ -o  $(OUT_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -shared -fpic

$(OBJ_PATH)GetFactoryConfig.o : GetFactoryConfig.cpp mkobjdir
	g++ -o $(OBJ_PATH)GetFactoryConfig.o $(CFLAGS) $(VPATH) \
	    -c GetFactoryConfig.cpp

$(OBJ_PATH)nx_ga_model_access_Interface.o : nx_ga_model_access_Interface.cpp
	g++ -o $(OBJ_PATH)nx_ga_model_access_Interface.o $(CFLAGS) $(VPATH) \
	    -c  nx_ga_model_access_Interface.cpp 


mkobjdir:
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists;";   else mkdir -p $(OBJ_PATH); fi

mksylibdir:
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists";   else mkdir -p $(OUT_PATH); fi
	
.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OUT_PATH)$(OBJ_NAME) $(OBJS)
