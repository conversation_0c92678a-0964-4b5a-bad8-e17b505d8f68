#pragma once

#include "os_platform_def.h"
#include "os_platform_fun.h"
#include "ftplib.h"

#define max_len 256

typedef struct 
{
	u_int8	typeID;
	u_int8	num;
	u_int8	firstAddr;
	u_int8	secondAddr;
	u_int8	content[20];
}set_save_type;

typedef struct  
{
	char dev_ip[max_len];
	char user_name[max_len];
	char password[max_len];
	char src_file[max_len];
	char src_path[max_len];
	char des_file[max_len];
}setting_file_info;

class CProSetting  
{
public:
	CProSetting();
	virtual ~CProSetting();

public:
	void init(setting_file_info* ftpfileinfo);
	bool get_settings(netbuf* control, vector<set_save_type> &vec_setting);
	bool get_file(netbuf* control);
	bool get_ftp_status(netbuf* control);
	bool get_set(vector<set_save_type> &vec_setting);

private:
	string m_src_file;
	string m_src_path;
	string m_des_file;
	string m_dev_ip;
	string m_user_name;
	string m_password;

};