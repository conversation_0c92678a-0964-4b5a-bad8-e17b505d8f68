#pragma warning(disable:4786)
#pragma warning(disable:4275)

#include "ProSetting.h"

CProSetting::CProSetting()
{

}

CProSetting::~CProSetting()
{

}

void CProSetting::init(setting_file_info* ftpfileinfo)
{
	m_dev_ip = ftpfileinfo->dev_ip;
	m_user_name = ftpfileinfo->user_name;
	m_password = ftpfileinfo->password;
	
	m_src_file = ftpfileinfo->src_file;
	m_des_file = ftpfileinfo->des_file;
	m_src_path = ftpfileinfo->src_path;
}

bool CProSetting::get_settings(netbuf* control, vector<set_save_type> &vec_setting)
{
	if (!get_file(control))
	{
		return false;
	}
	
	if (!get_set(vec_setting))
	{
		return false;
	}
	
	return true;
}

bool CProSetting::get_file(netbuf* control)
{
	if (!get_ftp_status(control))
	{
		return false;
	}
	
	if (sy_dirfile_exist(m_des_file.c_str())==0)
	{
		remove(m_des_file.c_str());
	}
	
	if (!FtpGet(m_des_file.c_str(), m_src_file.c_str(), 'I', control)==1)
	{
		return false;
	}
	
	return true;
	
}

bool CProSetting::get_ftp_status(netbuf* control)
{
	char str_path[max_len];
	memset(str_path, 0, max_len);
	
	if(control != NULL)
	{
		if (FtpPwd(str_path, max_len, control))	
		{
			return true;
		}
		
		FtpQuit(control);
		control = NULL;
	}
	
	if (FtpConnect(m_dev_ip.c_str(), &control))
	{	
		if (FtpLogin(m_user_name.c_str(),m_password.c_str(), control))
		{
			return true;
		}
	}
				
	if (control != NULL)
	{
		FtpQuit(control);
		control=NULL;
	}
	
	return false;
}

bool CProSetting::get_set(vector<set_save_type> &vec_setting)
{
	FILE* p_file = fopen(m_des_file.c_str(), "r+b");
	if (p_file==NULL)
	{
		return false;
	}

	while (!feof(p_file))
	{
		set_save_type setting;
		memset(&setting, 0, sizeof(set_save_type));

		fread(&setting, 1, sizeof(set_save_type), p_file);
		vec_setting.push_back(setting);
	}

	return true;
}