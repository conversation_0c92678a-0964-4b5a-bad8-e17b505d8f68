﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="CoIED.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\IED.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="xj_define.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\FindSortFuns.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\FailReason.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ftplib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ProFileList.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ProSetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\GADirect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CoDirect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\nx_ga_common\IED.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ThreadManger.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_comtrade.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_dfrlist.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\Checkup.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\I_IED_Run.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CoDirect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\FailReason.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ftplib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CoIED.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ProFileList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ProSetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\GADirect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ga_xj_ftp_wgl600.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
</Project>