﻿/****************************************************************
*  CoIED.h       author: gcy      date: 05/09/2013
*----------------------------------------------------------------
*  note: 实现许继电气WGL600录波器采集规约类
*  
*****************************************************************/

#pragma once

//#include <bitset>
#include "IED.h"
#include "ga_dfrlist.h"
#include "xj_define.h"
#include "FindSortFuns.h"
#include "ga_comtrade.h"
//#include "ftplib.h"
#include "ProFileList.h"
#include "ProSetting.h"
#include "ga_functions.h"


/** @brief   CPU属性迭代器*/
typedef vector<LD_ATBUTE>::iterator it_Ld;


/**
*@defgroup   许继电气WGL600录波器采集规约类
*@{
*/

/**
*@brief      
*<AUTHOR>
date:        30/08/2014
*
*example
*@code*
*    
*    
*    
*    
@endcode
*/
#pragma once
class CCoIED : public CIED
{
public:
	CCoIED(void);

	/**
	* @brief     		构造函数
	* @param[out]		pFactory，采集工厂属性指针
	* @param[out]		p_st_Ied, IED属性指针
	* @param[out]		pCoIEDLog，日志属性指针
	* @return			无
	*/
	CCoIED(FACTORY_ATBUTE* pFactory, IED_ATBUTE* p_st_Ied, CLogRecord* pCoIEDLog);

	~CCoIED(void);

	/**
	* @brief			召唤通讯状态
	* @param[in]		
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallAllLDState();

	/**
	* @brief			召唤指定LD的定值
	* @param[in]		nld,LD编号
	* @param[in]		zone,定值区号: -1:表示召唤当前区定值
	* @return			TRUE：成功， FALSE：失败                       
	*/
	BOOL CallSG(int nld, int zone);

	/**
	* @brief			召唤指定LD的文件列表(当pStart_time = NULL &&  pEnd_time = NULL, 表示召唤当前最新文件列表 )
	* @param[in]		nld	LD编号
	* @param[in]		pStart_time	起始时间   
	* @param[in]		pEnd_time	结束时间
	* @return			TRUE：成功， FALSE：失败		           
	*/
	BOOL CallFileList(int nld, MY_TIME_INFO* pStart_time, MY_TIME_INFO* pEnd_time);

	/**
	* @brief			召唤指定的波形文件
	* @param[in]		filename 需要召唤的文件名称
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallFile(string& filename);

	/**
	* @brief     	    召唤所有新文件
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallAllFile();

	bool CallComtradeFile(DFR_FILE_INF&  stWaveFile, BOOL bManual);
private:
	/** @brief	FTP连接句柄*/
	netbuf* m_hConnect;

	/** @brief  在请求时间段内的录波列表   */
	vector<DFR_FILE_INF> m_vFileinTimeRange;

	/** @brief	录波列表读取 */
//	CProFileList m_RtuFileList;

	/**	@brief	录波定值读取 */
	CProSetting m_RtuSetting;

	/** @brief  当前cpu*/
	u_int8 m_nCurCpu;

	/**	@brief	录波文件扩展名 */
	vector<string> m_vExtName;

	/**	@brief	本地列表文件名(带全路径) */
	char m_szLocListFile[MAX_LEN];

	/**	@brief	装置列表文件名 */
	char m_szRmtListFile[MAX_LEN];

	/**	@brief	装置录波文件路径 */
	char m_szRmtFilePath[MAX_LEN];

	/**	@brief	本地定值文件名(带全路径) */
	char m_szLocSettingFile[MAX_LEN];

	/**	@brief	装置定值文件名 */
	char m_szRmtSettingFile[MAX_LEN];

	/**	@brief	装置定值文件路径 */
	char m_szRmtSettingPath[MAX_LEN];

	/** @brief			召唤录波方式   */
	u_int8 m_u_CallFileType;

	/** @brief			本地录波存放目录（绝对路径）*/
	string m_strWavePath;

	/** @brief          录波器工作目录*/
	string m_strWorkPath;

	/** @brief          装置有新录波标志*/
	bool m_bNewWaveFlag;

	/** @brief			录波文件列表处理类对象*/
	CDFRList* m_pCurDfrList;

	/** @brief          存储在本地的定值全路径*/
	string m_strLocalSGFile;


	int m_iSendSerialNumber;  //发送序列号
	int m_iRecvSerialNumber;  //接收序列号

private:
	/**
	* @brief     		初始化各项配置
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitIED();

	void InitIedState();

	BOOL InitFtp();

	BOOL GetFtpStatus();

	BOOL GetNewFile(DFR_FILE_INF* WaveFile);

	BOOL GetFileExt(DFR_FILE_INF* WaveFile);

	/**
	* @brief     	    创建通讯对象, 负责创建通讯需要的通讯对象(总线或者点对点)
	* @param[out]       pDllPack 通讯库包装对象
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CreateCommTranObj(CSYCommunDllPack* pDllPack);

	/**
	* @brief     	   获取LD属性结构指针
	* @param[in]       nLD，LD编号
	* @return          bool,true:成功,false:失败
	*/				   
	bool GetLdAttr(int nLD, it_Ld& itor);

	/**
	* @brief     	 重新登录
	* @param[in]     无
	* @param[out]    无
	* @return        BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL Relogin();

	/**
	* @brief		发送新文件通知订单
	* @param[in]    文件名是带路径,不带扩展名的
	* @return		无
	*/
	void SendFileINF(DFR_FILE_INF& stWaveFile);

	/**
	* @brief     	从绝对路径中得出文件名
	* @param[in]    strAbsPath 绝对路径
	* @return       std::string 不带路径的文件名
	*/
	std::string GetFileNameFromAbsPath( const string strAbsPath)
	{
		int nPathPos = strAbsPath.find_last_of('\\');
		if (nPathPos == string::npos)
		{
			nPathPos = strAbsPath.find_last_of('/');
		}
		//如果2种路径符号都未找到，原样返回
		if (nPathPos == string::npos)
		{
			return strAbsPath;
		}

		//string strPath = strAbsPath.substr(0, nPathPos);
		string strFileNameNoPath = strAbsPath.substr(nPathPos+1);
		return strFileNameNoPath;
	}

};
/** @} */ //CCoIED OVER