﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp" />
    <ClCompile Include="..\..\nx_ga_common\Checkup.cpp" />
    <ClCompile Include="..\..\nx_ga_common\FailReason.cpp" />
    <ClCompile Include="..\..\nx_ga_common\GADirect.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ga_comtrade.cpp" />
    <ClCompile Include="..\..\nx_ga_common\IED.cpp" />
    <ClCompile Include="..\..\nx_ga_common\I_IED_Run.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ThreadManger.cpp" />
    <ClCompile Include="CoDirect.cpp" />
    <ClCompile Include="CoIED.cpp" />
    <ClCompile Include="nx_ga_udp_snmp_modify_note.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="CoDirect.h" />
    <ClInclude Include="CoIED.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ga_udp_snmp.rc" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{********-41F0-4CB8-A059-72B33D42D733}</ProjectGuid>
    <RootNamespace>nx_ga_snmp_net</RootNamespace>
    <ProjectName>nx_ga_udp_snmp</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>..\..\..\..\..\nx_bin\debug\ga</OutDir>
    <IncludePath>.\;..\..\..\..\..\nx_common;..\..\..\..\..\platform_include\plm_common;..\..\..\..\..\platform_include\plm_commun;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\nx_ga_common;..\..\..\..\..\platform_include\plm_common;..\..\..\..\..\platform_include\plm_commun;..\..\..\..\..\nx_common\;.\</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>__PLATFORM_MS_WIN__;_WINDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\..\..\nx_lib\debug\</AdditionalLibraryDirectories>
      <AdditionalDependencies>sylib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <ProjectReference>
      <UseLibraryDependencyInputs>false</UseLibraryDependencyInputs>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>