﻿/****************************************************************
*  CoIED.h       author: yk      date: 18/04/2018
*----------------------------------------------------------------
*  note: 实现武仪录波器采集规约类
*  
*****************************************************************/

#ifndef _COIED_H_
#define _COIED_H_

#include <bitset>
#include "IED.h"
#include "ga_dfrlist.h"
#include "FindSortFuns.h"
#include "ga_comtrade.h"

#pragma pack(1)                       //pragma pack(1)和下面的pragma pack()是为了解决字节对齐的问题。如果没有这两句，包装时，中间会凭空多出一位。
struct Send_Message_Head              //发送报文头结构体（列目录、读文件）
{
	DWORD Synchronized_character;    //同步字符 EB 90 EB 90
	BYTE starting_character;         //起始字符02
	BYTE address_code;               //标志（地址码）01
	BYTE characteristic_code;        //特征码  列目录为76 读文件为78
	WORD group_num;                  //分组数
	WORD group_no;                   //分组序号
	BYTE message_length;             //报文长度
};                                   //这个结构体没有包括DATA、SUM代码和、结束字符
#pragma pack()


#pragma pack(1)
struct Send_Message_ReadFile_Head    //读文件第一帧报文结构体
{
	DWORD Synchronized_character;    //同步字符 EB 90 EB 90
	BYTE starting_character;         //起始字符02
	BYTE address_code;               //标志（地址码）01
	BYTE characteristic_code;        //特征码  读文件为78
	WORD group_num;                  //分组数
	WORD group_no;                   //分组序号
	BYTE message_length;             //报文长度
};                                   //同样没有包括DATA（文件名）、SUM代码和、结束字符
#pragma pack()	
	

struct DIR_LIST                      //列目录接收报文DATA结构体
{
	WORD wr_Date;                    //日期 
	WORD wr_Time;                    //时间
	long FileLength;                 //文件长度
	char Attr;                       //地址
	char FileName[13];               //文件名
};

struct FILE_INFO                    //列目录文件信息结构体  
{
	char chFileName[20];            //文件名
	MY_TIME_INFO tmFileTime;        //日期、时间
};

struct CallSG_Send_Message          //召唤定值发送报文结构体
{
	BYTE start_character;           //启动字符 68
	BYTE length_high;               //长度，高字节
	BYTE length_low;                //长度，低字节
	BYTE start_character_repeat;    //启动字符重复 68
	BYTE control_domain;            //控制域
	BYTE address_domain;            //地址域
	BYTE asdu_type;                 //ASDU类型 15H
	BYTE vsq;                       //VSQ 81H
	BYTE cot;                       //传送原因 2A 通用分类读命令
	BYTE address_repeat;            //地址
	BYTE fun;                       //功能类型
	BYTE inf;                       //信息序号
	BYTE RII;                       //返回信息标识符
	BYTE asdu_number;               //通用分类标识数目
	BYTE group;                     //组号 线路1-40 母线41-60
	BYTE item;                      //条目号 00
	BYTE kod;                       //KOD 01 读实际值
	BYTE cs;                        //这里不做校验，0就行
	BYTE end_character;             //结束字符16H	 
};

                            

struct ASDU10_HEARD    //ASDU报文头结构体
{
	u_int8 TYP;       //类型标识
	u_int8 VSQ;       //可变结构限定词
	u_int8 COT;       //传送原因
	u_int8 ADDR;      //地址
	u_int8 FUN;       //功能类型
	u_int8 INF;       //信息序号
	u_int8 RII;       //返回信息标识符
	u_int8 NGD;       //通用数据集数目   低6位为数目
};

struct GROUP_DATA     //报文数据头  
{
	u_int8 group;     //组号
	u_int8 item;      //条目号
	u_int8 kod;       //数据描述
	u_int8 data_type; //数据类型
	u_int8 data_size; //数据宽度
	u_int8 data_num;  //数据数目  最高位后续状态位 0后面未跟数据，1后面数据有相同RII
	string cur_value; //当前值，长度不固定

	GROUP_DATA()
	{
		group = 0;
		item = 0;
		kod = 0;
		data_type = 0;
		data_size = 0;
		data_num = 0;
		cur_value = "";
	}
};

struct ASDU10                       //ASDU10通用分类服务数据(读定值接收)
{
	ASDU10_HEARD  heard;            //ASDU10报文头结构体  0A 81 2A 00 FE F1 61 18 
	GROUP_DATA    GroupDataList;    //数据集 （01 00 01 03 04 01）18 00 00 00
};    

#pragma once
class CCoIED : public CIED
{
public:
	CCoIED(void);

	/**
	* @brief     		构造函数
	* @param[out]		pFactory，采集工厂属性指针
	* @param[out]		p_st_Ied, IED属性指针
	* @param[out]		pCoIEDLog，日志属性指针
	* @return			无
	*/
	CCoIED(FACTORY_ATBUTE* pFactory, IED_ATBUTE* p_st_Ied, CLogRecord* pCoIEDLog);

	~CCoIED(void);

	/**
	* @brief			召唤指定LD的定值
	* @param[in]		nld,LD编号
	* @param[in]		zone,定值区号: -1:表示召唤当前区定值
	* @return			TRUE：成功， FALSE：失败                       
	*/
	BOOL CallSG(int nld, int zone);

	/**
	* @brief			召唤指定LD的文件列表(当pStart_time = NULL &&  pEnd_time = NULL, 表示召唤当前最新文件列表 )
	* @param[in]		nld	LD编号
	* @param[in]		pStart_time	起始时间   
	* @param[in]		pEnd_time	结束时间
	* @return			TRUE：成功， FALSE：失败		           
	*/
	BOOL CallFileList(int nld, MY_TIME_INFO* pStart_time, MY_TIME_INFO* pEnd_time);

	/**
	* @brief			召唤指定的波形文件
	* @param[in]		filename 需要召唤的文件名称
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallFile(string& filename);

	/**
	* @brief     	    召唤所有新文件
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallAllFile();

	/**
	* @brief		对时
	* @return		TRUE：成功， FALSE：失败                    
	*/
	BOOL UpdateTime();

	/*召唤通讯状态*/
	BOOL CallState(int nld);

		/**
	* @brief     	 重新登录
	* @param[in]     无
	* @param[out]    无
	* @return        BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL Relogin();
private:
	/** @brief          当前cpu*/
	u_int8 m_nCurCpu;

	/** @brief			召唤录波方式   */
	u_int8 m_u_CallFileType;

	/** @brief			本地录波存放目录（绝对路径）*/
	string m_strWavePath;

	/** @brief          录波器工作目录*/
	string m_strWorkPath;

	/** @brief          装置有新录波标志*/
	bool m_bNewWaveFlag;

	/** @brief			录波文件列表处理类对象*/
	CDFRList* m_pCurDfrList;

	/** @brief			当前文件列表*/
	vector<DFR_FILE_INF> m_vCurFileList;

	/** @brief			待召唤文件列表*/
	vector<DFR_FILE_INF> m_vCallFileList;

	/** @brief			新文件列表*/
	vector<DFR_FILE_INF> m_vNewFileList;

	/** @brief          存储在本地的定值全路径*/
	string m_strLocalSGFile;

private:
	/**
	* @brief     		初始化各项配置
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitIED();

	/**
	* @brief     	初始化装置通信状态
	* @param[in]    无
	* @param[out]   无
	* @return       void
	*/
	void InitIedState();

	/**
	* @brief     	打开通讯端口
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL OpenCommun();

	/**
	* @brief     		初始化网络通讯
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitNetwork();

	/**
	* @brief     	    创建通讯对象, 负责创建通讯需要的通讯对象(总线或者点对点)
	* @param[out]       pDllPack 通讯库包装对象
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CreateCommTranObj(CSYCommunDllPack* pDllPack);

	/**
	* @brief		初始化所有LD的的通讯状态，并得到装置最终状态
	* @param		无
	* @return		无         
	*/
	void InitAllLDState();
	
	/**
	* @brief     		 发送报文
	* @param[in]     	 无
	* @param[out]    	 无
	* @return          	 int,0 成功 1失败
	*/
	int SendData();

	/**
	* @brief     	    从socket接收数据
	* @param[in]        无
	* @param[out]       无
	* @return           int，0成功 －1失败， －2 超时
	*/
	int RecvData();	


	/**
	* @brief     	    接收报文
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL ReadData();

	/**
	* @brief     	以下变量与列目录相关
	*/
	Send_Message_Head send_message_catalogue;        //将列目录发送报文结构体作为一个成员变量。CoIED类下的每个函数都可以使用。

	DIR_LIST recv_message_catalogue_data;       //DIR_LIST是列目录接收报文中的DATA部分结构体。

	char recv_message_catalogue_data_filename[13] ;  //定义一个字符型数组用来存放解析出来的文件名

	string st_recv_message_catalogue_data_filename_suffix;  //定义一个string用来执行去后缀操作
    string st_recv_message_catalogue_data_filename;         //定义一个string用来存放去后缀之后的文件名

	vector<FILE_INFO> m_vecFileList;                     //定义一个vector用来存放文件名和其对应的时间

	void Pack_CallFileList(WORD dwGroupNum, WORD dwGroupNo); //打包召唤文件列表报文
	void DataProcess_catalogue() ;               //解析召唤文件列表报文


	/**
	* @brief     	以下变量或函数与读文件相关
	*/
	string filename_temporary;                                   //用来存放上面下发的filename，这个变量的目的是不改变filename。
	string filename_dat;                                         //将上面下发的无后缀名的filename转化为filename.dat后存放在这个变量中
	string filename_hdr;                                         //hdr    
	string filename_cfg;                                         //cfg
	string filename_lne;

	Send_Message_ReadFile_Head send_message_file_first;          //第一帧读文件发送报文结构体     
	Send_Message_Head send_message_file_continue;                     //后续读文件发送报文结构体

	BYTE recv_message_file_data_code;                             //用来存放读文件接收报文中的CODE码
	WORD recv_message_file_data_groupnum;                       //用来存放读文件接收报文中的分组数
	WORD recv_message_file_data_groupno;                        //用来存放读文件接收报文中的分组序号
	BYTE recv_message_file_data_length;                         //用来存放读文件接收报文中的报文长度（减去6就是DATA部分的长度）

	void Pack_ReadFile_First(string &filename_suffix);           //包装第一帧读文件报文
	void Pack_ReadFile_Continue(WORD dwGroupNum,WORD dwGroupNo);  //包装后续读文件报文
	BOOL CallFile_suffix(string& path_filename_suffix,string& filename_suffix); //召唤不同后缀名的文件
	void DataProcess_ReadFile(char* pData, int& nSize);           //解析读文件报文


	/**
	* @brief     	以下变量或函数与读定值相关
	*/


	CallSG_Send_Message callsg_send_message;             //读定值发送报文结构体

	void Pack_CallSG(int i);                             //包装定值报文  参数为组号

	BOOL SendData_SG();                                  //发送召唤定值报文 

	BOOL ReadData_SG();                                  //读定值报文，因为和列目录读文件报文格式不一样，所以单独设一个函数

	BOOL DataProcess_CallSG();                           //解析定值报文


	ICommuTransObj* m_pCommuTransObj_SG;                 //用来创建B网IP、端口

	BOOL OpenCommun_SG();                                //打开B网IP、端口（A网IP、端口在初始化函数中就打开了，B网在召定值时打开）

	void SetSaveDataFormat();                            //经过重载的函数，用来在调试窗口输出定值功能的收发及结果记录

	/**
	* @brief     	以下变量或函数与手动录波相关
	*/
	BOOL TriggerOsc(int nld);                            //手动录波
	void Pack_TriggerOsc();                              //包装手动录波报文

};

#endif