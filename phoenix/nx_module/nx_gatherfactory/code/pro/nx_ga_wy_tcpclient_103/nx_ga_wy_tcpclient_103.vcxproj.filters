﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="CoIED.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CoDirect.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\GADirect.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\I_IED_Run.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\IED.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ThreadManger.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\Checkup.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\FailReason.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_comtrade.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_dfrlist.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="nx_ga_wy_tcpclient_103_modify_note.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="CoDirect.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CoIED.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\IED.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>资源文件</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ga_wy_tcpclient_103.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>