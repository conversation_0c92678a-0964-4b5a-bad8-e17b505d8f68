#include "CoDirect.h"
#include <malloc.h>

int __Find3HeadFromCharList(char* c_buffer, int buffersize, char* c_3Char)
{
	int nResult = -1;
	for(int i = 0;i < buffersize-2;  i++)
	{
		if(c_3Char [0] == c_buffer[i] && c_3Char [1]   == c_buffer [i+1] && c_3Char [2]   == c_buffer [i+2])
		{
			nResult = i;
			break;
		}
	}
	return  nResult;
}

void OnListenClient(void* pRegObj, ICommuTransObj* pCommunTransObj, PUB_NETWORK_ADDR& PubNetWorkAddr)
{
	CCoDirect* pCoDirect = (CCoDirect*)pRegObj;
	pCoDirect->CreateListenTransObj(pCommunTransObj, PubNetWorkAddr);
}


CCoDirect::CCoDirect(void)
{
	m_buff_lst.clear();
	m_pTcpCommuTransObj = NULL;
	m_pBusCommuTransObj = NULL;
	m_pFactory = NULL;
	m_bExit =false;
	m_ThreadDeque.clear();
	bFlag=false;
}


CCoDirect::~CCoDirect(void)
{
	m_buff_lst.clear();
	if (m_pBusCommuTransObj != NULL)
	{
		m_pBusCommuTransObj->ClosePort();
		m_pSyCommunDallPack->DeleteCommuTranObj(m_pBusCommuTransObj);
		m_pBusCommuTransObj = NULL;
	}

	if (m_pTcpCommuTransObj != NULL)
	{
		m_pTcpCommuTransObj->ClosePort();
		m_pSyCommunDallPack->DeleteCommuTranObj(m_pTcpCommuTransObj);
		m_pTcpCommuTransObj = NULL;
	}	

	m_pFactory = NULL;
	bFlag=false;
	m_ThreadDeque.clear();
}
/***********************************************************************
* @brief     	计算接收到的数据是否合法
* @param[out]   pBuf 数据缓冲区	
* @param[in]    nLen 数据缓冲区长度
* @return       int	 = 0：成功， -1：失败
***********************************************************************/
int CCoDirect:: CheckRecvData(u_int8* pBuf, int nLen)
{
	BOOL bReault = FALSE;
	if (pBuf[0] == 0x00 && pBuf[1] == 0x67 && pBuf[2] == 0x7A && pBuf[3] == 0x78)
	{
		//m_pGaLog->FormatLogOut(CLogRecord::errorlog, "准备检验数据是否合法");
		bReault = CheckData(pBuf,nLen);
	}
	if (bReault == FALSE)
	{
		return -1;
	}
	return 0;
}

/***********************************************************************
* @brief     	计算接收到的数据是否合法
* @param[out]   pBuf 数据缓冲区	
* @param[in]    nLen 数据缓冲区长度	
* @return       BOOL,TRUE:成功    FALSE:失败
***********************************************************************/
BOOL CCoDirect::CheckData(u_int8* pBuf, int nLen)
{
	//校验校验和
	int CheckSum = CalCheck(&pBuf[0], nLen);
	//m_pGaLog->FormatLogOut(CLogRecord::errorlog, "校验校验和CheckSum:%d",CheckSum);
	if (CheckSum != pBuf[nLen-1])
	{
		return FALSE;
	}
	return TRUE;
}
/***********************************************************************
* @brief						计算校验和:检验和占双字节
* @param[in] pBuff				需要校验帧的起始位置
* @param[in] nLen				需要校验帧的长度
* @return						校验和
***********************************************************************/
u_int8 CCoDirect::CalCheck(u_int8* pBuff, int nLen)
{
	u_int16 nCalSum = 0;
	u_int8	nCalSumL = 0;
	u_int8	nCalSumH = 0;
	for (u_int16 i = 0; i < nLen-1; i++)
	{
		nCalSum += pBuff[i];
		
	}
	nCalSumH = (nCalSum >> 8) & 0x00FF;
	nCalSumL =  nCalSum & 0x00FF;
	return nCalSumL;
	
}

int gFun_CheckRevData(u_int8 *pBuff, int nLen, void *pCoIedPara)
{
	CCoDirect* pCoIed = (CCoDirect*)pCoIedPara;

	return pCoIed->CalCheck(pBuff, nLen);

	
}
int gFun_GetRemainDataLen(u_int8 *pBuf, int nLen, void *pParam)
{
	
	return 1;
}
int gFun_GetFrameDataAddr(u_int8 *pBuf, int nLen, void *pParam)
{
	return 1;
}
/***********************************************************************
* @brief     	 结束线程
* @param[in]     OS_PTHREAD_HANDLE &handle
* @param[in]     OS_PTHREAD_ID &ThreadId
* @return       
***********************************************************************/
void CCoDirect::end_thread(OS_PTHREAD_HANDLE &handle, OS_PTHREAD_ID &ThreadId)
{
#ifdef __PLATFORM_OPEN_LINUX__
	char strLog[255]="";
	int nRet = 0;
	if(handle != 0)
	{
		nRet=sy_wait_single_thread_exit(handle, NULL);
		if(nRet != 0)
		{
			sprintf(strLog,"[CCoDirect] exit thread exception,reason:%s", strerror(errno));
			m_pGaLog->FormatLogOut( CLogRecord::errorlog,strLog);
		}
		else
		{
			sprintf(strLog,"[CCoDirect] 线程成功结束!");
			m_pGaLog->FormatLogOut( CLogRecord::errorlog,strLog);
		}

		handle = INVALID_PTHREAD;
		ThreadId=0;
	}
#endif

#ifdef __PLATFORM_MS_WIN__
	//int nCount =0;
	if (handle != INVALID_PTHREAD)
		sy_close_thread(handle);

	handle = INVALID_PTHREAD;
	ThreadId=0;
	m_pGaLog->FormatLogOut( CLogRecord::errorlog,"[CCoDirect] 线程成功结束, CLogRecord::errorlog");

#endif
}

BOOL CCoDirect::Init(MODEL_RUN_PARAM* pRunParam)
{
	//初始化参数
	if (pRunParam == NULL)
	{
		return FALSE;
	}
	else
	{
		m_pModleRunParam = pRunParam;
		m_pThreadManger = new CThreadManger(pRunParam->pGaLog);
		m_pGaLog = pRunParam->pGaLog;
		m_pSyCommunDallPack = new CSYCommunDllPack(m_pGaLog);
	}

	//加载订单处理共享库
	if (!LoadOrderLibrary())
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog,"加载订单库失败");
		return FALSE;
	}

	//加载通讯共享库
	if (m_pSyCommunDallPack->Load() == FALSE)
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog,"加载通讯共享库失败");
		return FALSE;
	}

	if (CreateCommTranObj(m_pSyCommunDallPack) == FALSE)
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog,"创建监听对象失败");
		return FALSE;
	}

	CLogRecord*	pIEDLog = NULL;
	IED_RUN_PARAM* pIEDRunParam = NULL;

	//创建所有IED对象
	for (unsigned int i = 0; i < pRunParam->pFactory->ied_list.size(); i++)
	{
		//创建IED线程运行参数
		pIEDRunParam = new IED_RUN_PARAM;
		if (pIEDRunParam == NULL)
		{
			return FALSE;
		}

		//创建IED log对象
		pIEDLog = CreateIEDLog(&pRunParam->pFactory->ied_list[i], pRunParam->pFactory);

		//创建IED对象
		pIEDRunParam->pIED = CreateIED(pRunParam->pFactory, &pRunParam->pFactory->ied_list[i], pIEDLog);
		pIEDRunParam->pGaLog = m_pGaLog;
		pIEDRunParam->pIEDLog = pIEDLog;


		//添加到IED列表
		m_IEDlist.push_back(pIEDRunParam);
	}
	return TRUE;
}

BOOL CCoDirect::CreateCommTranObj(CSYCommunDllPack* pDllPack)
{
	if(m_pModleRunParam->pFactory->base_inf.gather_type == TYPE_TCP_SERVER)
	{
		//创建TCP监听对象
		if (CreateTcpSeverCommTranObj(m_pSyCommunDallPack) == FALSE)
		{	//TODO 监听失败直接退出进程，解决仅IED线程退出，factory进程在空转的情况。但不能exit()确定会不会造成内存泄露
			//raise(SIGTERM);
			m_pModleRunParam->pThreadInf->thread_state = THREAD_STATE_EXIT;
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, "Factory%d 进程准备退出！", 
				m_pModleRunParam->pFactory->base_inf.id);
			exit(-1);
			return FALSE;
		}
	}
	else
	{
		//创建串口通讯对象
		if (m_pModleRunParam->pFactory->base_inf.gather_type == TYPE_COM)
		{
			m_pBusCommuTransObj = m_pSyCommunDallPack->CreateComTranObj(&m_pFactory->base_inf.com_param, m_pGaLog, OBJ_MODLE_P2P);
		}
		//串口服务器
		else if (m_pModleRunParam->pFactory->base_inf.gather_type = TYPE_SERIAL_SERVER)
		{
			COMMU_TCP_CLIENT_PARAM	ClientParam;
			memset(&ClientParam, 0, sizeof(COMMU_TCP_CLIENT_PARAM));

			//备机无效
			ClientParam.TagetServer.bBckupSrvIsValid = false;
			memcpy(&ClientParam.TagetServer.TrunkSrvAddr, &m_pFactory->base_inf.ip_param, sizeof(PUB_DUAL_SERVER_ADDR));
			ClientParam.TagetServer.TrunkSrvAddr.bBckupNetIsValid = false;
			ClientParam.TimeOut = m_pFactory->base_inf.TimeOut;
			//m_pBusCommuTransObj = m_pSyCommunDallPack->CreateTcpClientTranObj(&ClientParam, m_pGaLog, OBJ_MODEL_BUS);
			m_pBusCommuTransObj = m_pSyCommunDallPack->CreateTcpClientTranObj(&ClientParam, m_pGaLog, OBJ_MODLE_P2P);
		}
		ICommuTransObj* pCommuTransObj = NULL;
		if (m_pBusCommuTransObj != NULL)
		{
			COMMU_BUS_REG_PARAM BusParam;
			BusParam.nHeadLen = 3;
			BusParam.bReadLast = FALSE;
			BusParam.pDev = (void*)this;
			BusParam.pFunGetRemainFrameLen = NULL;
			BusParam.pFunGetFrameRtuAddr = gFun_GetFrameDataAddr;
			BusParam.pCheckDeviceData = gFun_CheckRevData;

			////注册总线参数
			//if (m_pBusCommuTransObj->RegisterBusCommuParam(&BusParam) != 0)
			//{
			//	return FALSE;
			//}

			//m_pBusCommuTransObj->SetOption(RECV_TMOUT, 2000);

			//设置报文报文模式
			SetSaveDataFormat(pCommuTransObj);
			//SetSaveDataFormat();
		}
		//设置报文报文模式
		

		if (NULL == m_pBusCommuTransObj)
		{
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, "创建串口通讯对象失败，Factory%d 进程准备退出！", 
				m_pModleRunParam->pFactory->base_inf.id);
			return -1;
		}
	}

	return TRUE;
}

BOOL CCoDirect::CreateTcpSeverCommTranObj(CSYCommunDllPack* pDllPack)
{
	if (m_pTcpCommuTransObj == NULL)
	{
		COMMU_TCP_SERVER_PARAM TcpServerParam;
		memset(&TcpServerParam, 0, sizeof(COMMU_TCP_SERVER_PARAM));
		memcpy(&TcpServerParam.LocalListenAddr, &m_pModleRunParam->pFactory->base_inf.ip_param.BckUpNetAddr, sizeof(PUB_NETWORK_ADDR));
		TcpServerParam.TimeOut.RevTimeout = m_pModleRunParam->pFactory->base_inf.TimeOut.RevTimeout;
		if (TcpServerParam.TimeOut.RevTimeout == 0)
		{
			TcpServerParam.TimeOut.RevTimeout = 2000;
		}
		TcpServerParam.TimeOut.SendTimeout = m_pModleRunParam->pFactory->base_inf.TimeOut.SendTimeout;
		if (TcpServerParam.TimeOut.SendTimeout == 0)
		{
			TcpServerParam.TimeOut.SendTimeout = 2000;
		}

		m_pTcpCommuTransObj = pDllPack->CreateTcpServerTranObj(&TcpServerParam, m_pModleRunParam->pGaLog, OBJ_MODLE_P2P);
		m_pTcpCommuTransObj->SetOption(ADDR_REUSE, 1);

		m_pGaLog->FormatLogOut(CLogRecord::errorlog, "Factory%d 创建TCP服务端监听对象%s, IP:%s, PORT:%d", 
			m_pModleRunParam->pFactory->base_inf.id, 
			(m_pTcpCommuTransObj==NULL)?"失败":"成功", 
			m_pModleRunParam->pFactory->base_inf.ip_param.BckUpNetAddr.cIP, 
			m_pModleRunParam->pFactory->base_inf.ip_param.BckUpNetAddr.nPortNo);
		
		if (m_pTcpCommuTransObj == NULL)
		{
			return FALSE;
		}

		//打开TCP通讯
		OPEN_PORT_RESULT nResult = R_UNKNOWN_FAIL;
		nResult = m_pTcpCommuTransObj->OpenPort(OBJ_MODLE_P2P);
		if (nResult != R_SUCCESS)
		{
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, "Factory%d 打开TCP服务端监听端口失败", m_pModleRunParam->pFactory->base_inf.id);
			return FALSE;
		}	
		else
		{
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, "Factory%d 打开TCP服务端监听端口成功", m_pModleRunParam->pFactory->base_inf.id);
			return TRUE;
		}	
	}

	return TRUE;
}

BOOL CCoDirect::StartRun(MODEL_RUN_PARAM* pRunParam)
{
	try
	{
		
		m_pFactory = pRunParam->pFactory;
		if (Init(pRunParam) == FALSE)
		{
			m_pGaLog->FormatLogOut(CLogRecord::errorlog,"初始化init失败");
			return FALSE;
		}
		//启动所有IED运转线程
		if (StartAllIED() == FALSE)
		{
			m_pGaLog->FormatLogOut(CLogRecord::errorlog,"启动startallied失败");
			return FALSE;
		}

		//等待所有IED设备运转
		sy_sleep(8000);
		//创建报文处理线程
		if (Start_MsgHdlthread() == FALSE)
		{
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, "创建报文处理线程失败");
			return FALSE;
		}

		//如果是tcp服务端
		if (m_pFactory->base_inf.gather_type == TYPE_TCP_SERVER)
		{
			//初始化报文接收线程信息
			if (!_InitThreadInfo())
			{
				return FALSE;
			}
			//启动报文接收线程
			if (!StartReadThread())
			{
				return FALSE;
			}

			//注册TCP通讯
			if (RegistTcpSeverCommTranObj(m_pSyCommunDallPack) == FALSE)
			{
				m_pGaLog->FormatLogOut(CLogRecord::errorlog, "注册TCP服务对象失败");
				return FALSE;
			}
		}
		else
		{
			//如果是串口或者串口服务器
			//创建报文接收线程
			if (Start_MsgReadthread() == FALSE)
			{
				m_pGaLog->FormatLogOut(CLogRecord::errorlog, "创建串口通讯报文接收线程失败");
				return FALSE;
			}
		}

		while (pRunParam->pThreadInf->thread_state == THREAD_STATE_RUN)
		{
			m_bExit = false;
			

			//处理装置通信状态（当装置在召唤录波时）
			ProcessCommuState();

			//检查总线通讯端口是否异常,并进行端口打开
			ReOpenBusTranObj();

			sy_sleep(2000);
		}
		bFlag =false;
		m_bExit = true;

		m_pGaLog->FormatLogOut(CLogRecord::errorlog, "出现异常退出");
		//释放收报文资源
		ReleaseSource();
		//停止报文接收线程
		end_thread(m_hMsgReadThreadHandle, m_MsgReadThreadId);

		//停止报文处理线程
		end_thread(m_hMsgHdlThreadHandle, m_MsgHdlThreadId);

		//停止所有IED运转线程
		StopAllIED();

		//释放资源
		DestroyResource();

		return TRUE;
	}
	catch(...)
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog, "在函数CDirect::StartRun()中出现异常,异常信息是%s", sy_get_system_error_des());
		return FALSE;
	}
}


void CCoDirect::ReOpenBusTranObj()
{
	for (UINT i = 0; i < m_IEDlist.size(); i++)
	{
		if (m_IEDlist[i]->pIED->GetOnLineFlag() == TRUE)
		{
			return;
		}
	}

	//如果所有装置离线, 重新打开总线模式端口
	ReopenBusCommun();
}

BOOL CCoDirect::ReopenBusCommun()
{
	return TRUE;

	if (m_pBusCommuTransObj == NULL)
	{
		return FALSE;
	}

	//先关闭总线对象
	m_pBusCommuTransObj->ClosePort();

	//打开
	m_pBusCommuTransObj->OpenPort(OBJ_MODEL_BUS, 5000);
}

/**
* @brief         初始化业务相关线程信息
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CCoDirect::_InitThreadInfo()
{
	char cError[255]="";

	// 初始化召唤命令处理的线程池
	int i ;
	for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
	{
		m_CallPoolParam[i].nIndex = i;
		m_CallPoolParam[i].bFree  = true;
		m_CallPoolParam[i].bThreadStart = false;
		m_CallPoolParam[i].pReserve = NULL;
		m_CallPoolParam[i].bExit = true;
	}

	// 首次增加2个线程到线程管理队列
	if( !_StartProCmdThreadPool(THREAD_POOL_INIT_START_NUM,false) )
	{
		return false;
	}

	return true;
}

/**
* @brief         启动报文接收线程
* @param[in]     无
* @return        bool true-成功 false-失败
*/
bool CCoDirect::StartReadThread()
{
	char cError[255]      = "";
	int nRet              =0;       // 返回值
	CMyDeque < EC_THREAD_INFO *>::iterator ite,iteEnd;
	EC_THREAD_INFO * pThreadInfo = NULL;

	ite = m_ThreadDeque.begin();
	iteEnd   = m_ThreadDeque.end();

	// 获取线程参数并启动
	while ( ite != iteEnd )
	{
		nRet = 0;
		pThreadInfo = (EC_THREAD_INFO *)(*ite);
		if( NULL == pThreadInfo )
		{
			++ite;
			continue;
		}

		if( pThreadInfo->nState == T_S_START )    // 已经启动的不再启动
		{
			++ite;
			continue;
		}
		// 创建线程
		nRet = sy_create_thread(&pThreadInfo->hThreadHandle,
			&pThreadInfo->nThreadID,
			(SY_THREAD_FUNCTION)__ProxyThreadPro,
			pThreadInfo );

		if(nRet !=0)
		{
			sprintf(cError,"创建%s线程失败,原因:%s(%d)",pThreadInfo->strThreadDes.c_str(),strerror(errno),errno);
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
			return false;
		}

		sprintf(cError,"创建%s线程成功",pThreadInfo->strThreadDes.c_str());
		pThreadInfo->nState = T_S_START;
		m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);

		++ite;
		sy_sleep(10);
	}
	sprintf(cError,"StartReadThread()中全部线程(%d个)启动成功",m_ThreadDeque.size());
	m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);

	return true;
}

/**
* @brief         释放资源
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CCoDirect::ReleaseSource()
{
	char cError[255]="";
	int i =0;
	// 等待线程退出
	int n=0;
	for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
	{
		if( m_CallPoolParam[i].bThreadStart )
		{
			sy_unname_sem_post(&m_CallPoolParam[i].hSem);  // 激活线程
			n= 0;
			while( !m_CallPoolParam[i].bExit ) // 确认是否退出
			{
				_ZERO_MEM(cError,255);
				sprintf(cError,"index=%d的召唤命令处理线程退出标识=%d,等待...",i,m_CallPoolParam[i].bExit);
				m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
				sy_sleep(500);
				n++;
				if( n > 10)   // 最多等待5秒
				{
					sprintf(cError,"index=%d的召唤命令处理线程%d秒内未退出,不再等待",i,n*500/1000);
					m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
					break;
				}
				sy_unname_sem_post(&m_CallPoolParam[i].hSem); // 再发送信号
			}
			// 销毁信号
			sy_unname_sem_destroy(&m_CallPoolParam[i].hSem);
			m_CallPoolParam[i].bThreadStart = false;

			_ZERO_MEM(cError,255);
			sprintf(cError,"销毁index=%d的召唤命令处理线程信号量",i);
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
		}
	}

	// 停止线程
	StopAllReadThread();

	m_pGaLog->FormatLogOut(CLogRecord::errorlog, "ReleaseSource():停止线程并释放资源完毕");
	return true;
}

/**
* @brief         启动指定个数的收报文处理线程并加入线程管理队列
* @param[in]     int nThreadNum:指定的个数
* @param[in]     bool bStart: true-启动并加入管理队列 false-仅加入管理队列,不启动线程
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CCoDirect::_StartProCmdThreadPool(IN int nThreadNum,IN bool bStart) 
{
	char cError[510]="";
	char cDes[100]="";
	EC_THREAD_INFO * pThreadInfo = NULL;
	int i=0,nStartNum = 0;

	// 个数合法性判断
	if( nThreadNum >= THREAD_POOL_MAX_CAPACITY )
		return false;

	for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
	{
		if( m_CallPoolParam[i].bThreadStart )     // 已经启动
			continue;

		if( sy_unname_sem_init(&m_CallPoolParam[i].hSem,0) != 0 )
		{
			sprintf(cError,"_StartProCmdThreadPool():初始化inxdex=%d召唤命令线程参数的无名信号量失败,原因:%s(%d)",
				i,strerror(errno),errno);
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
			return false;
		}

		//启动线程
		pThreadInfo = new EC_THREAD_INFO;
		if( pThreadInfo == NULL)
		{
			sprintf(cError,"为index=%d的召唤命令线程分配线程信息内存失败,无法初始化",i);
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
			sy_unname_sem_destroy(&m_CallPoolParam[i].hSem);
			return false;
		}

		pThreadInfo->pSelfObj         = this;
		_ZERO_MEM(cDes,100);
		sprintf(cDes,"index=%d的收报文理线程_StartProCmdThreadPool()",i);
		pThreadInfo->strThreadDes     =cDes;
		pThreadInfo->pCallBackFunc    =_OnReadThreadExec;
		pThreadInfo->pParam           = &m_CallPoolParam[i];
		if( bStart ) 
		{
			if( !StartOneThread(pThreadInfo) )
			{
				delete pThreadInfo;
				sy_unname_sem_destroy(&m_CallPoolParam[i].hSem);
				return false;
			}
		}
		// 加入线程管理队列
		AddThreadInfo(pThreadInfo);
		pThreadInfo = NULL;

		// 修改启动标识
		m_CallPoolParam[i].bThreadStart = true;
		nStartNum++;
		if( nStartNum == nThreadNum )
			break;
	}

	if( nStartNum != nThreadNum )
	{
		sprintf(cError,"_StartProCmdThreadPool():启动%d个线程,小于要求个数(%d),可能线程池用完",nStartNum,nThreadNum);
		m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
		if(nStartNum <= 0 )
			return false;
	}
	return true;
}

/**
* @brief         增加线程参数信息
* @param[in]     EC_THREAD_INFO * pThreadInfo:线程参数结构
* @return        bool true-成功 false-失败
*/
bool CCoDirect::AddThreadInfo( EC_THREAD_INFO* pThreadInfo)
{
	m_ThreadDeque.push_back( pThreadInfo );
	return true;
}

/**
* @brief         启动参数指定的线程
* @param[in]     EC_THREAD_INFO * pThreadInfo:线程结构
* @return        bool true-成功 false-失败
*/
bool CCoDirect::StartOneThread(IN EC_THREAD_INFO * pThreadInfo)
{
	char cError[255]      = "";
	int nRet              =0;       // 返回值

	if( NULL == pThreadInfo )
	{
		sprintf(cError,"StartOneThread()时，输入参数为NULL");
		m_pGaLog->FormatLogOut(CLogRecord::errorlog,cError);
		return false;
	}

	// 创建线程
	nRet = sy_create_thread(&pThreadInfo->hThreadHandle,
		&pThreadInfo->nThreadID,
		(SY_THREAD_FUNCTION)__ProxyThreadPro,
		pThreadInfo );

	if(nRet !=0)
	{
		sprintf(cError,"创建%s线程失败,原因:%s(%d)",pThreadInfo->strThreadDes.c_str(),strerror(errno),errno);
		m_pGaLog->FormatLogOut(CLogRecord::errorlog,cError);
		return false;
	}

	sprintf(cError,"创建%s线程成功",pThreadInfo->strThreadDes.c_str());
	pThreadInfo->nState = T_S_START;
	m_pGaLog->FormatLogOut(CLogRecord::errorlog,cError);

	return true;
}

/*
*  @brief      线程函数
*  @param 		LPVOID * pParam:线程参数
*  @return 	bool true-成功 false-失败
*/
SY_THREAD_FUNCTION CCoDirect::__ProxyThreadPro(LPVOID * pParam)
{
	char cError[255]    = "";

	if( NULL == pParam )
		return THREAD_RET_VALUE;

	EC_THREAD_INFO * pThreadInfo = ( EC_THREAD_INFO *)pParam;

	if( pThreadInfo->pSelfObj == NULL )
	{
		printf("__ProxyThreadPro()中%s线程参数中源对象指针为NULL,无法启动\n",pThreadInfo->strThreadDes.c_str());
		return THREAD_RET_VALUE;
	}

	if(  pThreadInfo->pCallBackFunc == NULL )
	{
		sprintf(cError,"__ProxyThreadPro()中%s线程要执行线程任务的回调函数为NULL,无法启动\n",
			pThreadInfo->strThreadDes.c_str());
		printf(cError);
		return THREAD_RET_VALUE;
	}

	try
	{
		pThreadInfo->pCallBackFunc(pThreadInfo->pSelfObj,pThreadInfo->pParam);
	}
	catch(...)
	{
		sprintf(cError,"%s线程运行异常,退出,原因:%s:(%d)\n",
			pThreadInfo->strThreadDes.c_str(),strerror(errno),errno);
		printf(cError);
		return THREAD_RET_VALUE;
	}

	sprintf(cError,"%s线程正常退出\n",pThreadInfo->strThreadDes.c_str());
	printf(cError);
	return THREAD_RET_VALUE;
}

int CCoDirect::_OnReadThreadExec(LPVOID pObj,LPVOID pParam)
{
	char cError[510]    = "";

	if( ( NULL == pObj ) || (pParam == NULL ) )
		return THREAD_RET_VALUE;
	COMMUOBJ_HANDLE_PARAM * pCmd = (COMMUOBJ_HANDLE_PARAM *)pParam;

	CCoDirect * pProtocol = (CCoDirect *)pObj;

	try
	{
		pProtocol->__ReadLoop(pCmd,pProtocol);
	}
	catch(...)
	{
		sprintf(cError,"召唤命令处理线程池中index=%d的线程异常退出,原因:%s:(%d)",
			pCmd->nIndex,strerror(errno),errno);
		pProtocol->m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
	}

	return THREAD_RET_VALUE;
}

/**
* @brief         获取一个空闲的收报文线程
* @param[in]     无
* @param[out]    无
* @return        CALL_CMD_HANDLE_PARAM:召唤线程参数结构
*/
COMMUOBJ_HANDLE_PARAM * CCoDirect::_GetOneReadThread()
{
	int i = 0;
	for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
	{
		if( m_CallPoolParam[i].bThreadStart && m_CallPoolParam[i].bFree )   // 已经启动其空闲
		{
			m_CallPoolParam[i].bFree = false;
			return &m_CallPoolParam[i];
		}
	}

	// 没有找到时启动1个
	if( _StartProCmdThreadPool(1) )
	{
		for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
		{
			if( m_CallPoolParam[i].bThreadStart && m_CallPoolParam[i].bFree )   // 已经启动且为空闲
			{
				m_CallPoolParam[i].bFree = false;
				return &m_CallPoolParam[i];
			}
		}
	}
	m_pGaLog->FormatLogOut(CLogRecord::errorlog, "_GetOneReadThread()获取线程失败，可能线程池用尽");
	return NULL;
}

int CCoDirect::__ReadLoop(IN COMMUOBJ_HANDLE_PARAM * pParam,IN CCoDirect *pObj)
{
	char cError[510]="";
	if (pParam == NULL ||pObj == NULL)
	{
		return -1;
	}
	BUS_FRAME_OP frame_data;
	READ_BUFF readbuff_lst;
	COMMUOBJ_HANDLE_PARAM * pCommuParam = (COMMUOBJ_HANDLE_PARAM *) pParam;
	CCoDirect * pCoDirect = (CCoDirect *)pObj;

	if (pCoDirect->bFlag == false)
	{
		pCommuParam->bFree =true;
		pCommuParam->bThreadStart =false;
		sy_unname_sem_destroy(&pCommuParam->hSem);
		return 0;
	}
	//退出标识置为false
	pCommuParam->bExit = false;
	sprintf(cError,"__ReadLoop():收报文线程池中index=%d的线程开始运行,退出标识:%d",pParam->nIndex,pParam->bExit);
	m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);

	while(sy_unname_sem_wait(&pCommuParam->hSem)==0)
	{
		_ZERO_MEM(cError,510);
		sprintf(cError,"__ReadLoop():收报文线程池中index=%d的线程被激活",pCommuParam->nIndex);
		m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);

		if (pCoDirect->m_bExit)
		{
			break;
		}
		while (pCommuParam->ProCommuObj != NULL)
		{
			int nResult =pCommuParam->ProCommuObj->DirectRevData(frame_data.Buff, 114);
			if (nResult > 0)
			{
				vector<unsigned char> v_temp(frame_data.Buff,frame_data.Buff+nResult);
				readbuff_lst.nlength += nResult;
				readbuff_lst.read_buff.insert(readbuff_lst.read_buff.end(),v_temp.begin(),v_temp.end());
				memset(frame_data.Buff, 0, sizeof(frame_data.Buff));
				v_temp.clear();
			}
			else
			{
				pCommuParam->ProCommuObj->ClosePort();
				pCoDirect->m_pSyCommunDallPack->DeleteCommuTranObj(pCommuParam->ProCommuObj);
				pCommuParam->ProCommuObj = NULL;
			}
		}

		if (readbuff_lst.read_buff.size() != 0)
		{

			CAutoLockOnStack FrameLock (&pCoDirect->m_LockMsgList);
			pCoDirect->m_buff_lst.push_back(readbuff_lst);
		}

		vector<unsigned char>().swap(readbuff_lst.read_buff);
		readbuff_lst.nlength = 0;

		pCommuParam->bFree = true;  // 置为空闲状态

		sprintf(cError,"__ReadLoop():收报文线程池中index=%d的线程完成当前任务,置为空闲",pCommuParam->nIndex);
		m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
		sy_sleep(10);

	}

	readbuff_lst.read_buff.clear();
	readbuff_lst.nlength = 0;
	pCommuParam->bFree = true;
	pCommuParam->bExit = true;
	sprintf(cError,"__ReadLoop()：index=%d的收报文线程正常退出,退出标识置位(%d)",pCommuParam->nIndex,pCommuParam->bExit);
	m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);
	return 0;
}


/**
* @brief         停止所有线程,并释放线程信息资源
* @param[in]     无
* @return        bool true-成功 false-失败
*/
bool  CCoDirect::StopAllReadThread()
{
	char   cError[255]    = "";     // 错误信息
	int    nRet           = 0;
	EC_THREAD_INFO * pThreadInfo = NULL;

	while( !m_ThreadDeque.empty() )
	{
		pThreadInfo = (EC_THREAD_INFO*)m_ThreadDeque.front();

		if( NULL == pThreadInfo )
		{
			m_ThreadDeque.pop_front();
			continue;
		}

		// 结束非运行态的线程
		if( pThreadInfo->nState != T_S_STOP)
		{
			nRet = sy_wait_single_thread_exit(pThreadInfo->hThreadHandle,5,NULL);
			if( 0 != nRet )
			{
				sprintf(cError,"停止%s线程失败,返回值:%d,原因:%s(%d)",
					pThreadInfo->strThreadDes.c_str(),nRet,strerror(errno),errno);
			}
			else
			{
				sprintf(cError,"停止%s线程成功",pThreadInfo->strThreadDes.c_str());
			}
			m_pGaLog->FormatLogOut(CLogRecord::errorlog, cError);

# ifdef __PLATFORM_MS_WIN__
			sy_close_thread(pThreadInfo->hThreadHandle);
# endif
		}

		m_ThreadDeque.pop_front();

		// 释放资源
		delete pThreadInfo;
	}

	return true;
}

BOOL CCoDirect::RegistTcpSeverCommTranObj(CSYCommunDllPack* pDllPack)
{
	if (m_pTcpCommuTransObj == NULL)
	{
		return FALSE;
	}

	if (m_pTcpCommuTransObj->RegisterListenCallBack(this,OnListenClient,NULL) != 0)
	{
		return FALSE;
	}
	m_pGaLog->FormatLogOut(CLogRecord::tracelog, "注册TCP服务端监听回调成功, 监听IP:%s, PORT:%d", 
		 m_pFactory->base_inf.ip_param.BckUpNetAddr.cIP, m_pFactory->base_inf.ip_param.BckUpNetAddr.nPortNo);
	return TRUE;
}

/*************************************************************
* @brief		创建TCP通讯服务端对象
* @param[in]	pTmpListenObj	通讯对象指针
* @param[out]	void
* @return		void
**************************************************************/
void CCoDirect::CreateListenTransObj(ICommuTransObj* pTmpListenObj, PUB_NETWORK_ADDR& PubNetWorkAddr)
{
	string strip = PubNetWorkAddr.cIP;
	ICommuTransObj* pCommuTransObj = NULL;
	pCommuTransObj = m_pSyCommunDallPack->CreateListenTranObj(pTmpListenObj, m_pGaLog, OBJ_MODLE_P2P);
	if (pCommuTransObj == NULL)
	{
		m_pGaLog->FormatLogOut(CLogRecord::tracelog, "创建服务端(子站)与客户端(IP:%s, PORT:%d)之间的通讯对象失败", 
				PubNetWorkAddr.cIP, PubNetWorkAddr.nPortNo);
		return;
	}
	else
	{
		m_pGaLog->FormatLogOut(CLogRecord::tracelog, "创建服务端(子站)与客户端(IP:%s, PORT:%d)之间的通讯对象成功", 
			PubNetWorkAddr.cIP, PubNetWorkAddr.nPortNo);
	}
	pCommuTransObj->SetOption(RECV_TMOUT,m_pFactory->base_inf.TimeOut.RevTimeout);
	SetSaveDataFormat(pCommuTransObj);

	bFlag = true;

	COMMUOBJ_HANDLE_PARAM * pParam = _GetOneReadThread();
	if (pParam == NULL)
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog,"线程池用尽，本次收报文失败");
		return;
	}

	pParam->ProCommuObj = pCommuTransObj;

	if (sy_unname_sem_post(&pParam->hSem) == 0)
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog,"分配收报文线程成功");
	}
	else
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog,"分配收报文线程失败");
		return;
	}
	return;
}

/**
* @brief						设置报文保存模式
* @return						无
* @note                         
*/
void CCoDirect::SetSaveDataFormat(ICommuTransObj* pCommuTransObj)
{
	//设置报文保存模式
	FRAME_DATA_SAVE_FORMAT DataFormat;
	DataFormat.DataType = m_pFactory->out_param.data_format = 1 ? 1: 0;
	//如果不输出文件
	if (m_pFactory->out_param.dataout_flag == 0)
	{
		DataFormat.Days = 0;
	}
	else
	{
		DataFormat.Days = m_pFactory->out_param.rcd_datas;
	}
	DataFormat.FilePath = m_pFactory->out_param.log_data_path;
	DataFormat.FrameLen = m_pFactory->out_param.frame_data_len;
	DataFormat.SaveFormat = m_pFactory->out_param.dataout_flag == 1 ? 0 : 1;

	//模块名称
	char buf[256];
	sprintf(buf, "GA%d_data", m_pFactory->base_inf.id);
	DataFormat.ModelName = buf;
	DataFormat.PrinfFlag = m_pFactory->out_param.print_flag;

	if (pCommuTransObj != NULL)
	{
		pCommuTransObj->SetSaveDataFormat(DataFormat);
	}

	if (m_pBusCommuTransObj != NULL)
	{
		m_pBusCommuTransObj->SetSaveDataFormat(DataFormat);
	}
}
/*************************************************************
* @brief     	启动报文处理线程线程（串口通讯）
* @param[in]     无
* @param[out]    无
* @return       BOOL true-成功 false-失败
**************************************************************/
BOOL CCoDirect::Start_MsgHdlthread()
{
	BOOL bresult = FALSE;

	if (0 != sy_create_thread(&m_hMsgHdlThreadHandle, &m_MsgHdlThreadId,MsgHdlThreadFun, this))
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog, "启动报文处理线程失败");
		return bresult;
	}

	bresult = TRUE;
	return bresult;
}

/*************************************************************
* @brief     	启动报文接收线程线程（串口通讯）
* @param[in]     无
* @param[out]    无
* @return       BOOL true-成功 false-失败
**************************************************************/
BOOL CCoDirect::Start_MsgReadthread()
{
	BOOL bresult = FALSE;

	if (0 != sy_create_thread(&m_hMsgReadThreadHandle, &m_MsgReadThreadId,MsgReadThreadFun, this))
	{
		m_pGaLog->FormatLogOut(CLogRecord::errorlog, "启动串口通讯报文接收线程失败");
		return bresult;
	}

	bresult = TRUE;
	return bresult;
}

/***********************************************************************
* @brief     	串口通讯报文接收线程函数
* @param[in]    pParam 指向CCoIED的指针
* @return       static OS_PTHREAD_FUNC_RETTYPE WINAPI 0: 成功，其它：失败
***********************************************************************/
OS_PTHREAD_FUNC_RETTYPE WINAPI CCoDirect::MsgReadThreadFun(LPVOID pParam)
{
	OS_PTHREAD_FUNC_RETTYPE nRet = 0;
	CCoDirect * pObj = (CCoDirect *) pParam;

	if (pObj != NULL)
	{
		pObj->MsgReadLoop();
	}
	pObj->m_pGaLog->FormatLogOut(CLogRecord::errorlog, "报文处理线程退出");

	return nRet;
}

/***********************************************************************
* @brief     	报文处理线程函数
* @param[in]    pParam 指向CCoIED的指针
* @return       static OS_PTHREAD_FUNC_RETTYPE WINAPI 0: 成功，其它：失败
***********************************************************************/
OS_PTHREAD_FUNC_RETTYPE WINAPI CCoDirect::MsgHdlThreadFun(LPVOID pParam)
{
	OS_PTHREAD_FUNC_RETTYPE nRet = 0;
	CCoDirect * pObj = (CCoDirect *) pParam;

	if (pObj != NULL)
	{
		pObj->MsgHdlLoop();
	}
	pObj->m_pGaLog->FormatLogOut(CLogRecord::errorlog, "报文处理线程退出");

	return nRet;
}

/***********************************************************************
* @brief     	 报文接收线程
* @param[in]     无
* @param[out]    无
* @return       int,0:成功,-1:失败
***********************************************************************/
int CCoDirect::MsgReadLoop()
{
	int nRet = 0;
	char cLog[255]="";
	BOOL channelstate = TRUE;
	m_pGaLog->FormatLogOut(CLogRecord::tracelog, "进入串口通讯报文接收线程");
	m_pBusCommuTransObj->ClosePort();
	//此处修改为串口点对点通讯
	if (R_SUCCESS == m_pBusCommuTransObj->OpenPort(OBJ_MODLE_P2P, 5000))
	{
		m_pGaLog->FormatLogOut(CLogRecord::tracelog, "OpenPort成功");
	}
	else
	{
		m_pGaLog->FormatLogOut(CLogRecord::tracelog, "OpenPort失败");
		return -1;
	}

	unsigned char cbuf[RevbufLen] = {0};
	READ_BUFF readbuff_lst;
	unsigned int uiRecBufSize = 0;
	unsigned long ulTimeBeforeFirstData = sy_get_tickcount();
	unsigned long  uTime_unstate= sy_get_tickcount();
	unsigned long ulTime_stateNow = 0;

	unsigned long ulTimeNow = 0;
	int nResult;
	u_long TimeOut = (atoi(m_pFactory->base_inf.param1.c_str()) == 0) ? 500 : atoi(m_pFactory->base_inf.param1.c_str());
	while (!m_bExit)
	{
		if (uiRecBufSize == 0)
		{
			//记录开始时间
			ulTimeBeforeFirstData = sy_get_tickcount();
		}
		else
		{
			ulTimeNow = sy_get_tickcount();
			if (ulTimeNow - ulTimeBeforeFirstData > TimeOut)
			{
				uiRecBufSize = 0;
				continue;
			}
		}

		//数组溢出标识
		if (uiRecBufSize > RevbufLen)
		{
			uiRecBufSize = 0;
			continue;
		}
		else
		{
			
			if (channelstate)
			{  				
				nResult = m_pBusCommuTransObj->DirectRevData(&cbuf[uiRecBufSize],1024);

			}else{
				ulTime_stateNow = sy_get_tickcount();
				if (ulTime_stateNow - uTime_unstate > 60000)
				{
					//点对点接收数据一次收取一个数据存放到cbuf里，将返回的结果累加，如果达到接收需要的报文长度则开始处理
					nResult = m_pBusCommuTransObj->DirectRevData(&cbuf[uiRecBufSize],1024);
				}
			}
			if (nResult<0)
			{
				channelstate = FALSE;
				uTime_unstate= sy_get_tickcount();
				nResult=0;
			}else if (nResult>0){
				channelstate = TRUE;
			}

			if (nResult > 0)
			{

				if (uiRecBufSize == 0)
				{
					ulTimeBeforeFirstData = sy_get_tickcount();
				}
				uiRecBufSize += nResult;
			}
			else
			{
				continue;
			}
		}

		if (1 == uiRecBufSize)
		{
			if (cbuf[0] != 0x00)																uiRecBufSize = 0;
		}
		else if (uiRecBufSize == 2)
		{
			if (cbuf[0] != 0x00 || cbuf[1] != 0x67)												uiRecBufSize = 0;
		}
		else if (uiRecBufSize == 3)
		{
			if (cbuf[0] != 0x00 || cbuf[1] != 0x67|| cbuf[2] != 0x7A)							uiRecBufSize = 0;
		}
		else if (uiRecBufSize == 4 || uiRecBufSize == 8)
		{
			if (cbuf[0] != 0x00 || cbuf[1] != 0x67 || cbuf[2] != 0x7A || cbuf[3] != 0x78)		uiRecBufSize = 0;
		}

		//接收到需要的报文长度
		if (uiRecBufSize >= RevmainLen)
		{

			//将接收到的报文放到接收报文缓冲区中
			vector<unsigned char> v_temp(cbuf,cbuf+uiRecBufSize);
			readbuff_lst.nlength += uiRecBufSize;
			readbuff_lst.read_buff.insert(readbuff_lst.read_buff.end(),v_temp.begin(),v_temp.end());
			v_temp.clear();

			ulTimeBeforeFirstData = ulTimeNow;
			uiRecBufSize = 0;
		}
		//将接收的缓冲区read_buff中的数据放到接收数据列表m_buff_lst中，为后续解报文做准备
		if (readbuff_lst.read_buff.size() != 0)
		{

			CAutoLockOnStack FrameLock (&m_LockMsgList);
			m_buff_lst.push_back(readbuff_lst);
		}
		vector<unsigned char>().swap(readbuff_lst.read_buff);
		readbuff_lst.nlength = 0;
		
	}
	readbuff_lst.read_buff.clear();
	readbuff_lst.nlength = 0;
	return 0;
}

/***********************************************************************
* @brief     	 报文处理线程
* @param[in]     无
* @param[out]    无
* @return       int,0:成功,1:失败
***********************************************************************/
int CCoDirect::MsgHdlLoop()
{
	int nRet = 0;
	char cLog[255] = "";

	char c_Head[3];
	c_Head[0] = CHAR_AAA_67;
	c_Head[1] = CHAR_AAA_7A;
	c_Head[2] = CHAR_AAA_78;

	m_pGaLog->FormatLogOut(CLogRecord::tracelog, "进入报文处理线程");

	CCoIED* pCCoIED= NULL;
	while(!m_bExit)
	{
		if (m_buff_lst.size() == 0)
		{
			sy_sleep(10);
			continue;
		}

		READ_BUFF frame;
		{
			//取第一条数据
			CAutoLockOnStack FrameLock (&m_LockMsgList);
			frame = m_buff_lst.front();
			m_buff_lst.erase(m_buff_lst.begin());
			m_buff_lst.swap(m_buff_lst);
		}

		//找第一个报文头，获取产品序列号，将报文推送过去
		if(frame.nlength > 13)//不是UART START
		{
			int p = -1;
			char charary[7000];
			if (frame.nlength >7000)
			{
				memcpy(charary,&(frame.read_buff[0]),7000);
				frame.nlength =7000;
			}
			else
			{
				memcpy(charary, &(frame.read_buff[0]),frame.nlength);
			}
			//找67 7A 78
			p = __Find3HeadFromCharList(charary,frame.nlength,c_Head);
			if (p != -1)
			{
				//获取产品序列号
				int32 iValue = 0;						
				iValue = (charary[p+4]*65536) +  (charary[p+5] *256) + (charary[p+6]);
				VER_DEV dev;
				dev.dev_no = (iValue & 0x1FFFFF);
				m_pGaLog->FormatLogOut(CLogRecord::tracelog,"产品序列号%d",dev.dev_no);
				for (unsigned int i = 0; i <m_IEDlist.size(); i++)
				{
					pCCoIED =(CCoIED*)m_IEDlist[i]->pIED;
					if (dev.dev_no == pCCoIED->m_nIedAddr)
					{
						pCCoIED->PushMsg(frame);
						m_pGaLog->FormatLogOut(CLogRecord::tracelog, "推送装置%d报文成功",pCCoIED->m_ied_id);
					}
				}	
			}
			else
			{
				m_pGaLog->FormatLogOut(CLogRecord::tracelog, "未找到报文头");
			}		
		}
		vector<unsigned char>().swap(frame.read_buff);
		frame.nlength = 0;
		sy_sleep(10);
	}

	m_buff_lst.clear();
	return 0;
}


