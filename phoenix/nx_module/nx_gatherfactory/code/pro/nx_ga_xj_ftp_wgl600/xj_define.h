﻿#pragma once

#define MAX_LEN 256

/**	 @brief	默认的录波文件路径 */
const string file_path_name = "\\data\\";

/**	 @brief	默认的列表文件名 */
const string list_file_name = "RecordsList.lst";

/**	 @brief	默认的定值文件名 */
const string setting_file_name = "DeviceSetting.set";

/*******************************************************************************************
						故障类型定义
********************************************************************************************/
/** @brief			   无故障 */
#define FAULT_NONE		0x0000
/** @brief			   A相接地故障*/
#define FAULT_AN		0x0001
/** @brief			   B相接地故障*/
#define FAULT_BN		0x0002
/** @brief			   C相接地故障*/
#define FAULT_CN		0x0004
/** @brief			   AB短路故障*/
#define FAULT_AB		0x0008
/** @brief			   BC短路故障*/
#define FAULT_BC		0x0010
/** @brief			   CA短路故障*/
#define FAULT_CA		0x0020
/** @brief			   AB短路接地故障*/
#define FAULT_ABN		0x0040
/** @brief			   BC短路接地故障*/
#define FAULT_BCN		0x0080
/** @brief			   CA短路接地故障*/
#define FAULT_CAN		0x0100
/** @brief			   三相短路故障故障*/
#define FAULT_ABC		0x0200
/** @brief			   三相短路接地故障故障*/
#define FAULT_ABCN		0x0400
/** @brief			   其它类型故障*/
#define FAULT_OTHER		0x0800
/** @brief			   母线故障
#define FAULT_BUS		0x1000
/** @brief			   区外故障*/
#define FAULT_OUT		0x2000
/** @brief			   变压器内部故障*/
#define FAULT_TRN		0x2001
/** @brief			   尚未分析*/
#define FAULT_NOANA		0x7fff
									

template <typename DBSrc, typename SettingData>
struct equal_SettingData : binary_function<DBSrc, SettingData, bool> 
{
	/**
	* @brief     	根据ga_id1 ga_id2 ga_id3 ga_id4检查点表配置
	* @param[in]    Lp,从数据库中读出来的相应点表的结构
	* @param[out]   Rp，报文中提取出来的需对比的结构
	* @return       bool,True:找到,False:未找到
	*/
	bool operator()(const DBSrc& Lp, const SettingData& Rp) const 
	{
		return (atoi(Lp.ga_id1.c_str()) ==Rp.typeID && 
			atoi(Lp.ga_id2.c_str()) == Rp.num &&
			atoi(Lp.ga_id3.c_str()) == Rp.firstAddr &&
			atoi(Lp.ga_id4.c_str())==Rp.secondAddr);
	}
};