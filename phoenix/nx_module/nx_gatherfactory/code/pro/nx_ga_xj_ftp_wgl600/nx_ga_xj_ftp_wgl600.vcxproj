﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4BEEDC82-2956-4EEF-8133-DC0E5418AEBD}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>nx_ga_xj_ftp_wgl600</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(IncludePath)</IncludePath>
    <OutDir>..\..\..\..\..\nx_bin\debug\ga\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\..\..\nx_bin\release\ga\</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_USE_32BIT_TIME_T;WIN32;_DEBUG;_WINDOWS;_USRDLL;NX_GA_XJ_FTP_WGL600_EXPORTS;__PLATFORM_MS_WIN__;_FTPLIB_NO_COMPAT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..\nx_ga_common;..\..\..\..\..\platform_include\plm_common;..\..\..\..\..\platform_include\plm_commun;..\..\..\..\..\nx_common\;.\</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
      <AdditionalDependencies>ws2_32.lib;sylib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\..\..\nx_lib\debug\</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_USE_32BIT_TIME_T;WIN32;NDEBUG;_WINDOWS;_USRDLL;NX_GA_XJ_FTP_WGL600_EXPORTS;__PLATFORM_MS_WIN__;_FTPLIB_NO_COMPAT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..\nx_ga_common;..\..\..\..\..\platform_include\plm_common;..\..\..\..\..\platform_include\plm_commun;..\..\..\..\..\nx_common\;.\</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>ws2_32.lib;sylib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\..\..\nx_lib\release\</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\nx_ga_common\FailReason.h" />
    <ClInclude Include="..\..\nx_ga_common\FindSortFuns.h" />
    <ClInclude Include="..\..\nx_ga_common\GADirect.h" />
    <ClInclude Include="..\..\nx_ga_common\IED.h" />
    <ClInclude Include="CoDirect.h" />
    <ClInclude Include="ftplib.h" />
    <ClInclude Include="CoIED.h" />
    <ClInclude Include="ProFileList.h" />
    <ClInclude Include="ProSetting.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="xj_define.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp" />
    <ClCompile Include="..\..\nx_ga_common\Checkup.cpp" />
    <ClCompile Include="..\..\nx_ga_common\FailReason.cpp" />
    <ClCompile Include="..\..\nx_ga_common\GADirect.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ga_comtrade.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ga_dfrlist.cpp" />
    <ClCompile Include="..\..\nx_ga_common\IED.cpp" />
    <ClCompile Include="..\..\nx_ga_common\I_IED_Run.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ThreadManger.cpp" />
    <ClCompile Include="CoDirect.cpp" />
    <ClCompile Include="CoIED.cpp" />
    <ClCompile Include="ftplib.c" />
    <ClCompile Include="ProFileList.cpp" />
    <ClCompile Include="ProSetting.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ga_xj_ftp_wgl600.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>