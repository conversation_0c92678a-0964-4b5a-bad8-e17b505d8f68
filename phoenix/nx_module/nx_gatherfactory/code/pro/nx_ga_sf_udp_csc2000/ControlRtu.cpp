// ControlRtu.cpp: implementation of the CControlRtu class.
//
//////////////////////////////////////////////////////////////////////

#include "ControlRtu.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

MUTEX g_mutex_lock;

CControlRtu::CControlRtu()
{
}

CControlRtu::CControlRtu(_RTU_THREAD_PARAM* pRtuThreadParam):CBaseControlRtu(pRtuThreadParam)
{
		xj_mutex_init(&g_mutex_lock);
}

CControlRtu::~CControlRtu()
{
	xj_mutex_destroy(&g_mutex_lock);
}

int CControlRtu::CreatAllThread()
{
	CreatAllRtuThread();

// 	_EndReadDatathread(m_pCommunObiect);
// 	_EndWriteDatathread(m_pCommunObiect);

	return 3;
}


