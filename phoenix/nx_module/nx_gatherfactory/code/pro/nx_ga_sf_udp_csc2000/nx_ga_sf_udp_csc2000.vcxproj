﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A9F79F4E-C9D5-4896-B6DF-C3D098F41101}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>nx_ga_nr_com_103</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(IncludePath)</IncludePath>
    <OutDir>..\..\..\..\..\nx_bin\debug\ga\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\..\..\..\nx_bin\release\ga\</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;NX_GA_SF_UDP_CSC2000_EXPORTS;__PLATFORM_MS_WIN__;_USE_32BIT_TIME_T;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.\;..\..\nx_ga_common;..\..\..\..\..\nx_common;..\..\..\..\..\platform_include\plm_common;..\..\..\..\..\platform_include\plm_commun</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\..\..\nx_lib\debug\</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;NX_GA_SF_UDP_CSC2000_EXPORTS;__PLATFORM_MS_WIN__;_USE_32BIT_TIME_T;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..\nx_ga_common;..\..\..\..\..\nx_common\;..\..\..\..\..\platform_include\plm_common;..\..\..\..\..\platform_include\plm_commun;.\</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>..\..\..\..\..\nx_lib\release\</AdditionalLibraryDirectories>
      <AdditionalDependencies>sylib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\nx_ga_common\FailReason.h" />
    <ClInclude Include="..\..\nx_ga_common\FindSortFuns.h" />
    <ClInclude Include="..\..\nx_ga_common\GADirect.h" />
    <ClInclude Include="..\..\nx_ga_common\ga_dfrlist.h" />
    <ClInclude Include="..\..\nx_ga_common\IED.h" />
    <ClInclude Include="ASDU.h" />
    <ClInclude Include="ASDU_common.h" />
    <ClInclude Include="ASDU_config.h" />
    <ClInclude Include="ASDU_CSC_0X30_analoginfo.h" />
    <ClInclude Include="ASDU_CSC_0X30_digitinfo.h" />
    <ClInclude Include="ASDU_CSC_0X33_settinginfo.h" />
    <ClInclude Include="ASDU_CSC_0X37.h" />
    <ClInclude Include="ASDU_CSC_0X38.h" />
    <ClInclude Include="ASDU_CSC_0X65.h" />
    <ClInclude Include="ASDU_CSC_0X66.h" />
    <ClInclude Include="ASDU_CSC_0X67.h" />
    <ClInclude Include="ASDU_CSC_0X68.h" />
    <ClInclude Include="ASDU_CSC_0X69.h" />
    <ClInclude Include="ASDU_CSC_0XA1.h" />
    <ClInclude Include="ASDU_CSC_0XA4.h" />
    <ClInclude Include="ASDU_CSC_0XA7.h" />
    <ClInclude Include="ASDU_CSC_0XB4_Baseinfo.h" />
    <ClInclude Include="ASDU_CSC_0XB4_Oscdata.h" />
    <ClInclude Include="ASDU_CSC_0XE5.h" />
    <ClInclude Include="ASDU_CSC_0XE9.h" />
    <ClInclude Include="ASDU_CSC_common.h" />
    <ClInclude Include="assistdef.h" />
    <ClInclude Include="CInf_Col.h" />
    <ClInclude Include="CoDirect.h" />
    <ClInclude Include="CoIED.h" />
    <ClInclude Include="csc2000_define.h" />
    <ClInclude Include="Distill_OSC_For_Old_Ver.h" />
    <ClInclude Include="Param_Info.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="rtu_util.h" />
    <ClInclude Include="Setting_Info.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp" />
    <ClCompile Include="..\..\nx_ga_common\Checkup.cpp" />
    <ClCompile Include="..\..\nx_ga_common\FailReason.cpp" />
    <ClCompile Include="..\..\nx_ga_common\GADirect.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ga_comtrade.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ga_dfrlist.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ga_functions.cpp" />
    <ClCompile Include="..\..\nx_ga_common\IED.cpp" />
    <ClCompile Include="..\..\nx_ga_common\I_IED_Run.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ThreadManger.cpp" />
    <ClCompile Include="ASDU.cpp" />
    <ClCompile Include="ASDU_common.cpp" />
    <ClCompile Include="ASDU_config.cpp" />
    <ClCompile Include="ASDU_CSC_0X30_analoginfo.cpp" />
    <ClCompile Include="ASDU_CSC_0X30_digitinfo.cpp" />
    <ClCompile Include="ASDU_CSC_0X33_settinginfo.cpp" />
    <ClCompile Include="ASDU_CSC_0X37.cpp" />
    <ClCompile Include="ASDU_CSC_0X38.cpp" />
    <ClCompile Include="ASDU_CSC_0X65.cpp" />
    <ClCompile Include="ASDU_CSC_0X66.cpp" />
    <ClCompile Include="ASDU_CSC_0X67.cpp" />
    <ClCompile Include="ASDU_CSC_0X68.cpp" />
    <ClCompile Include="ASDU_CSC_0X69.cpp" />
    <ClCompile Include="ASDU_CSC_0XA1.cpp" />
    <ClCompile Include="ASDU_CSC_0XA4.cpp" />
    <ClCompile Include="ASDU_CSC_0XA7.cpp" />
    <ClCompile Include="ASDU_CSC_0XE5.cpp" />
    <ClCompile Include="ASDU_CSC_0XE9.cpp" />
    <ClCompile Include="ASDU_CSC_common.cpp" />
    <ClCompile Include="assistdef.cpp" />
    <ClCompile Include="CoDirect.cpp" />
    <ClCompile Include="CoIED.cpp" />
    <ClCompile Include="Distill_OSC_For_Old_Ver.cpp" />
    <ClCompile Include="nx_ga_sf_udp_csc2000_modify_note.cpp" />
    <ClCompile Include="Param_Info.cpp" />
    <ClCompile Include="rtu_util.cpp" />
    <ClCompile Include="Setting_Info.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ga_sf_udp_csc2000.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>