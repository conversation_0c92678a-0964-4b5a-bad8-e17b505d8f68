
#include "Setting_Info.h"



int setting_info::packet( BYTE* pBuff )
{
  return 0;
}

void setting_info::unpacket( BYTE* pBuff, int& offset )
{
  setting_info ret_value ;
  memset(&ret_value, 0x00, sizeof( setting_info ) );

  ret_value.setting_code = *pBuff;
  pBuff += 1;
  offset = 1;

  ret_value.Low_byte_value = *pBuff;
  pBuff += 1;
  offset += 1;

  ret_value.high_byte_value = *pBuff;
  pBuff += 1;
  offset += 1;

}

