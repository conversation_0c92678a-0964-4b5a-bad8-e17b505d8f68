// ControlRtu.h: interface for the CControlRtu class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_CONTROLRTU_H__4E5ECE55_2EF0_4A39_A4FF_C5C242D284D3__INCLUDED_)
#define AFX_CONTROLRTU_H__4E5ECE55_2EF0_4A39_A4FF_C5C242D284D3__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "BaseControlRtu.h"
#include "rtu_util.h"


extern MUTEX g_mutex_lock;

class CControlRtu : public CBaseControlRtu  
{
public:
	CControlRtu();
	virtual ~CControlRtu();
	
	CControlRtu(_RTU_THREAD_PARAM* pRtuThreadParam);
	int CreatAllThread();


};

#endif // !defined(AFX_CONTROLRTU_H__4E5ECE55_2EF0_4A39_A4FF_C5C242D284D3__INCLUDED_)
