
#include "Param_Info.h"



int param_info::packet( BYTE* pBuff )
{
	return 0;
}

void param_info::unpacket( BYTE* pBuff, int& offset )
{
	param_code = *pBuff;
	pBuff += 1;
	offset = 1;

	param_property = *pBuff;
	pBuff += 1;
	offset += 1;

	param_value = cal_word_for_low_to_high(&pBuff[ 0 ] );
	pBuff += 2;
	offset += 2;
}

bool operator==( const param_info& lhs, const param_info& rhs ) 
{
	return lhs.param_code == rhs.param_code &&
			   lhs.param_property == rhs.param_property &&
			   lhs.param_value == rhs.param_value;
}
