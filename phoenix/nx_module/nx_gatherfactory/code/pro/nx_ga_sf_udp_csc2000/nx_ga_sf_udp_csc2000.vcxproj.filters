﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files\ASDU">
      <UniqueIdentifier>{cf5746b5-e8e6-4199-987c-9ef8cd8f65d8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\ASDU">
      <UniqueIdentifier>{b767eede-41ec-4980-88c7-6684211867c9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\nx_ga_common\IED.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\FindSortFuns.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CoIED.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CoDirect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\GADirect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\FailReason.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\ga_dfrlist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="csc2000_define.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CInf_Col.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Distill_OSC_For_Old_Ver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Param_Info.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="rtu_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Setting_Info.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="assistdef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ASDU.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_common.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_common.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_config.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X30_analoginfo.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X30_digitinfo.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X33_settinginfo.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X37.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X38.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X66.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X67.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X68.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0XA1.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0XA4.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0XA7.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0XB4_Baseinfo.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0XB4_Oscdata.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0XE5.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0XE9.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X65.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
    <ClInclude Include="ASDU_CSC_0X69.h">
      <Filter>Header Files\ASDU</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\nx_ga_common\IED.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ThreadManger.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_comtrade.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CoIED.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\Checkup.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_functions.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\I_IED_Run.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CoDirect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\GADirect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\FailReason.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_dfrlist.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Distill_OSC_For_Old_Ver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Param_Info.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rtu_util.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Setting_Info.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="assistdef.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ASDU.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_common.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_common.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_config.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X30_analoginfo.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X30_digitinfo.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X33_settinginfo.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X37.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X38.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X66.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X67.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X68.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0XA1.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0XA4.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0XA7.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0XE5.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0XE9.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="nx_ga_sf_udp_csc2000_modify_note.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X65.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
    <ClCompile Include="ASDU_CSC_0X69.cpp">
      <Filter>Source Files\ASDU</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ga_sf_udp_csc2000.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>Source Files</Filter>
    </None>
  </ItemGroup>
</Project>