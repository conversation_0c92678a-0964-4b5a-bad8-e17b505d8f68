#define the path of search
VPATH = -I./ \
				-I../../nx_ga_common \
				-I../../../../../nx_common \
				-I../../../../../platform_include/plm_common \
				-I../../../../../platform_include/plm_commun 
LABLE = 1.0.5

#cflags definition
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	OUT_PATH = ../../../../../nx_bin/release/ga/
	LIB_PATH = ../../../../../nx_lib/release/
	OBJ_PATH = ./release/
	OBJ_NAME = nx_ga_zd_com_jbk3000.so-$(LABLE)
else
	RUN_FLAGS = -g
	OUT_PATH = ../../../../../nx_bin/debug/ga/
	LIB_PATH = ../../../../../nx_lib/debug/
	OBJ_PATH = ./debug/
	OBJ_NAME = nx_ga_zd_com_jbk3000.so
endif

LIBS =  -L./ -L$(OUT_PATH) -lpthread -L$(LIB_PATH) -lsylib -ldl -rdynamic -lrt

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__

#object file definition
OBJS = $(OBJ_PATH)CoIED.o $(OBJ_PATH)CoDirect.o $(OBJ_PATH)IED.o $(OBJ_PATH)Checkup.o $(OBJ_PATH)GADirect.o \
$(OBJ_PATH)I_IED_Run.o $(OBJ_PATH)ThreadManger.o $(OBJ_PATH)ga_comtrade.o $(OBJ_PATH)FailReason.o \
$(OBJ_PATH)SYCommunDllPack.o $(OBJ_PATH)CsgLogRecord.o $(OBJ_PATH)CsgLogRecordMngr.o 

nx_ga_zd_com_jbk3000.so : $(OBJS) mkoutdir
	g++ -o  $(OUT_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -shared -fpic

$(OBJ_PATH)CoIED.o : ./CoIED.cpp mkobjdir
	g++ -c ./CoIED.cpp -o $(OBJ_PATH)CoIED.o $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)CoDirect.o : ./CoDirect.cpp
	g++ -c ./CoDirect.cpp -o $(OBJ_PATH)CoDirect.o $(CFLAGS) $(VPATH)

$(OBJ_PATH)IED.o : ../../nx_ga_common/IED.cpp
	g++ -c ../../nx_ga_common/IED.cpp -o $(OBJ_PATH)IED.o $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)Checkup.o : ../../nx_ga_common/Checkup.cpp
	g++ -c ../../nx_ga_common/Checkup.cpp -o $(OBJ_PATH)Checkup.o $(CFLAGS) $(VPATH)

$(OBJ_PATH)GADirect.o : ../../nx_ga_common/GADirect.cpp
	g++ -c ../../nx_ga_common/GADirect.cpp -o $(OBJ_PATH)GADirect.o $(CFLAGS) $(VPATH)

$(OBJ_PATH)I_IED_Run.o : ../../nx_ga_common/I_IED_Run.cpp
	g++ -c ../../nx_ga_common/I_IED_Run.cpp -o $(OBJ_PATH)I_IED_Run.o $(CFLAGS) $(VPATH)

$(OBJ_PATH)ThreadManger.o : ../../nx_ga_common/ThreadManger.cpp
	g++ -c ../../nx_ga_common/ThreadManger.cpp -o $(OBJ_PATH)ThreadManger.o $(CFLAGS) $(VPATH)

$(OBJ_PATH)ga_comtrade.o : ../../nx_ga_common/ga_comtrade.cpp
	g++ -c ../../nx_ga_common/ga_comtrade.cpp -o $(OBJ_PATH)ga_comtrade.o $(CFLAGS) $(VPATH)

$(OBJ_PATH)FailReason.o : ../../nx_ga_common/FailReason.cpp
	g++ -c ../../nx_ga_common/FailReason.cpp -o $(OBJ_PATH)FailReason.o $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)CsgLogRecord.o : ../../../../../nx_common/CsgLogRecord.cpp
	g++ -c ../../../../../nx_common/CsgLogRecord.cpp -o $(OBJ_PATH)CsgLogRecord.o $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)CsgLogRecordMngr.o : ../../../../../nx_common/CsgLogRecordMngr.cpp
	g++ -c ../../../../../nx_common/CsgLogRecordMngr.cpp -o $(OBJ_PATH)CsgLogRecordMngr.o $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)SYCommunDllPack.o : ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp
	g++ -c ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp -o $(OBJ_PATH)SYCommunDllPack.o $(CFLAGS) $(VPATH)

mkobjdir:
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists;";   else mkdir -p $(OBJ_PATH); fi

mkoutdir:
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists";   else mkdir -p $(OUT_PATH); fi

.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OUT_PATH)$(OBJ_NAME) $(OBJS)
