#define the path of search
VPATH = -I./ \
				-I../../nx_ga_common \
				-I../../../../../nx_common \
				-I../../../../../platform_include/plm_common \
				-I../../../../../platform_include/plm_commun 
LABLE = 1.0.2

#cflags definition
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	OBJ_PATH = ../../../../../nx_bin/release/ga/
	LIB_PATH = ../../../../../nx_lib/release/
	OBJ_NAME = nx_ga_ys_tcpclient_es1.so-$(LABLE)
else
	RUN_FLAGS = -g
	OBJ_PATH = ../../../../../nx_bin/debug/ga/
	LIB_PATH = ../../../../../nx_lib/debug/
	OBJ_NAME = nx_ga_ys_tcpclient_es1.so
endif

LIBS =  -L./ -L$(OBJ_PATH) -lpthread -L$(LIB_PATH) -lsylib -ldl -rdynamic -lrt

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__

#object file definition
OBJS = CoIED.o CoDirect.o IED.o Checkup.o GADirect.o I_IED_Run.o ThreadManger.o ga_comtrade.o ga_dfrlist.o FailReason.o \
SYCommunDllPack.o CsgLogRecord.o CsgLogRecordMngr.o 

nx_ga_ys_tcpclient_es1.so : $(OBJS) mksylibdir
	g++ -o  $(OBJ_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -shared -fpic

CoIED.o : ./CoIED.cpp
	g++ -c ./CoIED.cpp $(CFLAGS) $(VPATH)
	
CoDirect.o : ./CoDirect.cpp
	g++ -c ./CoDirect.cpp $(CFLAGS) $(VPATH)

IED.o : ../../nx_ga_common/IED.cpp
	g++ -c ../../nx_ga_common/IED.cpp $(CFLAGS) $(VPATH)
	
Checkup.o : ../../nx_ga_common/Checkup.cpp
	g++ -c ../../nx_ga_common/Checkup.cpp $(CFLAGS) $(VPATH)

GADirect.o : ../../nx_ga_common/GADirect.cpp
	g++ -c ../../nx_ga_common/GADirect.cpp $(CFLAGS) $(VPATH)

I_IED_Run.o : ../../nx_ga_common/I_IED_Run.cpp
	g++ -c ../../nx_ga_common/I_IED_Run.cpp $(CFLAGS) $(VPATH)

ThreadManger.o : ../../nx_ga_common/ThreadManger.cpp
	g++ -c ../../nx_ga_common/ThreadManger.cpp $(CFLAGS) $(VPATH)

ga_comtrade.o : ../../nx_ga_common/ga_comtrade.cpp
	g++ -c ../../nx_ga_common/ga_comtrade.cpp $(CFLAGS) $(VPATH)

ga_dfrlist.o : ../../nx_ga_common/ga_dfrlist.cpp
	g++ -c ../../nx_ga_common/ga_dfrlist.cpp $(CFLAGS) $(VPATH)

FailReason.o : ../../nx_ga_common/FailReason.cpp
	g++ -c ../../nx_ga_common/FailReason.cpp $(CFLAGS) $(VPATH)
	
CsgLogRecord.o : ../../../../../nx_common/CsgLogRecord.cpp
	g++ -c ../../../../../nx_common/CsgLogRecord.cpp $(CFLAGS) $(VPATH)
	
CsgLogRecordMngr.o : ../../../../../nx_common/CsgLogRecordMngr.cpp
	g++ -c ../../../../../nx_common/CsgLogRecordMngr.cpp $(CFLAGS) $(VPATH)
	
SYCommunDllPack.o : ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp
	g++ -c ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp $(CFLAGS) $(VPATH)

mksylibdir : 
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists";   else mkdir -p $(OBJ_PATH); fi
	
.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OBJ_PATH)$(OBJ_NAME) $(OBJS)
