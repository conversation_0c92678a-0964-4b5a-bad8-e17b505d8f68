﻿#ifndef ZH_DEFINE_H
#define ZH_DEFINE_H

/** @brief              发送报文的最大长度 */
#define SEND_MSG_MAX_LEN 256
/** @brief				接收到的报文的最大长度 */	
#define RECV_MSG_MAX_LEN 16384

/** @brief              报文头长度*/
#define PACKET_HEAD_SIZE 8

/** @brief				装置中存放的文件列表的名称*/
#define ZH_RECNET_FILE_NAME	"RECENT.LST"

#define ZH_SETTING_FILE_NAME "ZHREC.FDZ.103.BIN"

/** @brief              读写模式中读模式 */
#define RW_READ          0

/** @brief              读写模式中写模式*/
#define RW_WRITE         1

/** @brief              读写模式中读写模式*/
#define RW_READ_WRITE    2


/** @brief              发送报文缓冲区*/
typedef struct MSG_BUFFER_SEND
{
	/** @brief          存放报文*/
	u_int8 u_Msg[SEND_MSG_MAX_LEN];
	/** @brief			报文长度*/
	int iMsgLen;
}_SEND_MSG;

/** @brief              接收报文缓冲区*/
typedef struct MSG_BUFFER_RECV
{
	/** @brief          存放报文*/
	u_int8 u_Msg[RECV_MSG_MAX_LEN];
	/** @brief			报文长度*/
	int iMsgLen;
}_RECV_MSG;

/** @brief              录波器返回包报文结构*/
typedef struct PACKET_RETURN
{
	/** @brief          错误码*/
	DWORD code;

	/** @brief          发送者*/
	DWORD sender;

	/** @brief          对错误码的文本表述*/
	char message[256];
}_PACKET_RETURN;

//从装置取文件的结构
typedef struct
{
	/** @brief          文件来源*/
	WORD from;			
	/** @brief          标记*/
	WORD flag;			
	/** @brief          文件偏移，用于断点续传*/
	DWORD offset;		
	/** @brief          根据flag域的区别含义不同*/
	long fid;			
	/** @brief          文件名*/
	char name[256];
}FILE_GET;

/** @brief              文件信息*/
typedef struct FILE_TRANS_INFO
{
	/** @brief          从装置取文件的结构*/
	FILE_GET GetFile;
	/** @brief			要存储的本地文件名（带路径）*/
	string strAbsLocalName;
}_FILE_TRANS_INFO;


/****************************************************************************
					录波器返回错误信息定义
*****************************************************************************/
/** @brief				远方已经关闭*/
#define	ECONN_REMOTECLOSE	71000				
/** @brief				连接太多*/
#define ECONN_TOOMANY		71001				
/** @brief				远方没有应答 */
#define ECONN_NOACK			71002				
/** @brief				数据包类型错误*/
#define ECONN_PTYPE			71003	
/** @brief				远方取消了当前操作 */
#define ECONN_REMOTECANCEL	71004
/** @brief				本地取消了当前操作*/
#define ECONN_LOCALCANCEL	71005		
/** @brief				没有找到指定的设备*/
#define ECONN_NOUNITID		71006				
/** @brief				创建文件失败*/
#define ECONN_FILECREATE	71200			
/** @brief				打开文件失败*/
#define ECONN_FILEOPEN		71201		
/** @brief				内存分配失败*/
#define ECONN_MEMORY		71202			
/** @brief				连接数据库失败*/
#define ECONN_DBCONN		71250		
/** @brief				动态库中无函数*/
#define ECONN_NODLLFUN		71300		
/** @brief				无效的命令（该命令系统不支持） */
#define ECONN_COMMAND		71301				
/** @brief				没有所请求的数据*/
#define	ECONN_NO_DATA		71302				
/** @brief				数据库异常*/
#define ECONN_DB_EXCEPTION	71303			
/** @brief				系统繁忙*/
#define ECONN_BUSY			71304	
/** @brief				参数错误或无效 */
#define ECONN_PARAM			71305	
/** @brief				成功（无错误）*/
#define ECONN_SUCCESS		0					


/****************************************************************************
						报文类型定义
*****************************************************************************/
/** @brief             对上一条命令的响应(PACKET_RETURN 结构)*/
#define PTYPE_RETURN		0x01 				
/** @brief			   设置/修改装置时钟*/
#define PTYPE_TIME			0x02				
/** @brief			   录波装置传送文件时的分包大小*/
#define PTYPE_PACKETSIZE	0x03				
/** @brief			   录波装置发送数据时祯间隔时间*/
#define PTYPE_FRAMEDELAY	0x04		
/** @brief			   单个文件的信息(FILE_INFO 结构)*/
#define PTYPE_FILE_INFO		0x10				
/** @brief             单个文件的数据*/
#define PTYPE_FILE_DATA		0x11				
/** @brief             请求指定的文件信息(FILE_GET 结构)*/
#define PTYPE_FILE_GET		0x12				
/** @brief             装置回应的文件信息(FILE_INFO 结构)*/
#define PTYPE_FILE_PUT		0x13			    
/** @brief             装置回应的文件版本信息(FILE_GET 结构)*/
#define PTYPE_FILE_VER		0x15				
/** @brief             对方传送指定的目录信息(FILE_GET 结构)*/
#define PTYPE_DIR_GET		0x22				
/** @brief             对方回应程序的工作目录*/
#define PTYPE_WORK_PATH		0x3A				
/** @brief             对方回应录波数据的存放目录*/
#define PTYPE_DATA_PATH		0x3B				
/** @brief             对方中止当前的传输任务*/
#define PTYPE_CANCEL		0x6E				
/** @brief             录波器的信息*/
#define PTYPE_RECORDERINFO  0x01000010			
/** @brief             录波器的型号*/
#define PTYPE_GETMODEL		0x01000025			
/** @brief             装置的新状态*/
#define PTYPE_GETNEWSTATUS	0x00000300			
/** @brief             录波器的新状态 */
#define PTYPE_RESETNEWSTATUS 0x00000301
/** @brief             遥控录波*/
#define PTYPE_RECORD		 0x000205

/*******************************************************************************************
						故障类型定义
********************************************************************************************/
/** @brief			   无故障 */
#define FAULT_NONE		0x0000
/** @brief			   A相接地故障*/
#define FAULT_AN		0x0001
/** @brief			   B相接地故障*/
#define FAULT_BN		0x0002
/** @brief			   C相接地故障*/
#define FAULT_CN		0x0004
/** @brief			   AB短路故障*/
#define FAULT_AB		0x0008
/** @brief			   BC短路故障*/
#define FAULT_BC		0x0010
/** @brief			   CA短路故障*/
#define FAULT_CA		0x0020
/** @brief			   AB短路接地故障*/
#define FAULT_ABN		0x0040
/** @brief			   BC短路接地故障*/
#define FAULT_BCN		0x0080
/** @brief			   CA短路接地故障*/
#define FAULT_CAN		0x0100
/** @brief			   三相短路故障故障*/
#define FAULT_ABC		0x0200
/** @brief			   三相短路接地故障故障*/
#define FAULT_ABCN		0x0400
/** @brief			   其它类型故障*/
#define FAULT_OTHER		0x0800
/** @brief			   母线故障
#define FAULT_BUS		0x1000
/** @brief			   区外故障*/
#define FAULT_OUT		0x2000
/** @brief			   变压器内部故障*/
#define FAULT_TRN		0x2001
/** @brief			   尚未分析*/
#define FAULT_NOANA		0x7fff
									


/*******************************************************************************************
						录波器各结构定义
********************************************************************************************/

/** @brief             文件头信息 */
typedef struct
{
	/** @brief			文件类型标记*/
	char flag[16];	
	/** @brief			文件版本号*/
	WORD ver;		
	/** @brief			分组数*/
	WORD ngrp;			
	/** @brief			保留*/
	BYTE r[32];			
}FDZ_ASDU10_BIN_HDR;

/** @brief				一个组的信息*/
typedef struct
{
	/** @brief			组编号*/
	BYTE gno;	
	/** @brief			该组中条目数*/
	BYTE ni;			
	/** @brief			该组的描述*/
	char descr[32];		
}FDZ_ASDU10_BIN_GRP;

/** @brief				一个条目的信息 */
typedef struct
{
	/** @brief			组编号*/
	BYTE gno;	
	/** @brief			条目号*/
	BYTE ino;			
	/** @brief			实际值的类型：3 表示整数；7 表示浮点数*/
	BYTE typ;			
	/** @brief			有效性标记：0 表示该项定值无效；1 表示该项定值有效*/
	BYTE flg;			
	/** @brief			该条目的描述*/
	char descr[64];		
	/** @brief			该条目的单位（量纲）*/
	char unit[8];		
}FDZ_ASDU10_BIN_ITM;

/** @brief				装置FAULT_INFO中时间结构*/
typedef struct
{
	/** @brief			时间的年份数*/
	WORD year;		
	/** @brief			1～12 时间的月份数*/
	WORD month;		
	/** @brief			1～12 时间的月份数*/
	WORD day;			
	/** @brief			0～23 时间的小时数*/
	WORD hour;			
	/** @brief			0～59 时间的分钟数*/
	WORD minute;		
	/** @brief			0～59 时间的秒数*/
	WORD second;	
	/** @brief			0～999 时间的毫秒数*/
	WORD ms;			
	/** @brief			0～999 时间的微秒数*/
	WORD us;			
}DATE_TIME;

//
/** @brief				文件信息 */
typedef struct FILE_INFO
{
	/** @brief			文件来源*/
	DWORD from;			
	/** @brief			文件时间（16字节）*/
	DATE_TIME time;		
	/** @brief			文件大小*/
	DWORD size;			
	/** @brief			文件属性*/
	DWORD attr;			
	/** @brief			发送的文件偏移*/
	DWORD offset;		
	/** @brief			发送的文件长度 */
	DWORD length;		
	/** @brief			 文件名*/
	char name[256];
}_FILE_INFO;

/** @brief				文件版本信息 */
typedef struct FILE_VER
{
	/** @brief			文件版本字符串，如"7.6.9.8"*/
	char ver[32];
	/** @brief			文件属性信息*/
	FILE_INFO info;
}_FILE_VER;

#pragma  pack(1)
/** @brief				故障文件信息结构*/
typedef struct FAULT_INFO
{
	/** @brief          文件类型*/
	WORD type;			
	/** @brief			数据文件数目（总为1）*/
	short datnum;		
	/** @brief			配置文件数目（未使用） */
	short connum;		
	/** @brief			故障编号*/
	long faultid;	
	/** @brief			文件大小*/
	DWORD filesize;	
	/** @brief			设备节点ID（未使用）*/
	long devid;			
	/** @brief			故障线路ID（未使用）*/
	long lineid;		
	/** @brief			子站节点ID（未使用）*/
	long staid;			
	/** @brief			故障类型*/
	DWORD faulttype;	
	/** @brief			故障距离（km）*/
	double fdist;		
	/** @brief			故障时间*/
	DATE_TIME  time;	
	/** @brief			故障元件名称*/
	char linename[64];  
	/** @brief			故障文件名称*/
	char filename[256]; 
	/** @brief			启动信息*/
	char sti[128];
}_FAULT_INFO;



template <typename DBSrc, typename SettingData>
struct equal_SettingData : binary_function<DBSrc, SettingData, bool> 
{
	/**
	* @brief     	根据ga_id1 ga_id2检查点表配置
	* @param[in]    Lp,从数据库中读出来的相应点表的结构
	* @param[out]   Rp，报文中提取出来的需对比的结构
	* @return       bool,True:找到,False:未找到
	*/
	bool operator()(const DBSrc& Lp, const SettingData& Rp) const 
	{
		//TODO ga_id1是string,不能直接和BYTE比较
		//return ((BYTE)atoi(Lp.ga_id1.c_str()) == Rp.gno) && ((BYTE)atoi(Lp.ga_id2.c_str()) == Rp.ino);
		return (Lp.group == Rp.gno) && ( Lp.item == Rp.ino);
	}
};

#endif //ZH_DEFINE_H