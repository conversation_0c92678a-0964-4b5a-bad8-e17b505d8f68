﻿/****************************************************************
*  CoIED.h       author: gcy      date: 05/09/2013
*----------------------------------------------------------------
*  note: 实现中元华电录波器采集规约类
*  
*****************************************************************/

#ifndef _COIED_H_
#define _COIED_H_

#include <bitset>
#include "IED.h"
#include "ga_dfrlist.h"
#include "zh_define.h"
#include "FindSortFuns.h"
#include "ga_comtrade.h"

/** @brief   CPU属性迭代器*/
typedef vector<LD_ATBUTE>::iterator it_Ld;

/**
*@defgroup   中元华电录波器采集规约类
*@{
*/

/**
*@brief      实现ZH系列录波器数据采集
*<AUTHOR>
date:        09/09/2013
*
*example
*@code*
*    
*    
*    
*    
@endcode
*/
#pragma once
class CCoIED : public CIED
{
public:
	CCoIED(void);

	/**
	* @brief     		构造函数
	* @param[out]		pFactory，采集工厂属性指针
	* @param[out]		p_st_Ied, IED属性指针
	* @param[out]		pCoIEDLog，日志属性指针
	* @return			无
	*/
	CCoIED(FACTORY_ATBUTE* pFactory, IED_ATBUTE* p_st_Ied, CLogRecord* pCoIEDLog);

	~CCoIED(void);

	/**
	* @brief			召唤指定LD的通讯状态
	* @param[in]		nld,LD编号
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallState(int nld);

	/**
	* @brief			召唤指定LD的定值
	* @param[in]		nld,LD编号
	* @param[in]		zone,定值区号: -1:表示召唤当前区定值
	* @return			TRUE：成功， FALSE：失败                       
	*/
	BOOL CallSG(int nld, int zone);

	/**
	* @brief			召唤指定LD的文件列表(当pStart_time = NULL &&  pEnd_time = NULL, 表示召唤当前最新文件列表 )
	* @param[in]		nld	LD编号
	* @param[in]		pStart_time	起始时间   
	* @param[in]		pEnd_time	结束时间
	* @return			TRUE：成功， FALSE：失败		           
	*/
	BOOL CallFileList(int nld, MY_TIME_INFO* pStart_time, MY_TIME_INFO* pEnd_time);

	/**
	* @brief			召唤指定的波形文件
	* @param[in]		filename 需要召唤的文件名称
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallFile(string& filename);

	/**
	* @brief     	    召唤所有新文件
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallAllFile();

	/**
	* @brief     	远方触发录波
	* @param[in]    需要触发的LD
	* @return       TRUE：成功， FALSE：失败
	*/
	BOOL TriggerOsc(int nld);

	/**
	* @brief		对时
	* @return		TRUE：成功， FALSE：失败                    
	*/
	BOOL UpdateTime();

	/**
	* @brief	    信号复位
	* @param[in]    nld 需要复位的LD
	* @return		TRUE：成功， FALSE：失败                       
	*/
	BOOL Reset(int nld);

private:
	/** @brief			IED属性结构指针  */
	//IED_ATBUTE* m_pIED;

	/** @brief          当前cpu*/
	u_int8 m_nCurCpu;

	/** @brief			召唤录波方式   */
	u_int8 m_u_CallFileType;

	/** @brief			本地录波存放目录（绝对路径）*/
	string m_strWavePath;

	/** @brief          发送报文缓冲区*/
	MSG_BUFFER_SEND m_stSendMsg;

	/** @brief          接收报文缓冲区*/
	MSG_BUFFER_RECV m_stRecvMsg;

	/** @brief          录波器工作目录*/
	string m_strWorkPath;

	/** @brief          装置有新录波标志*/
	bool m_bNewWaveFlag;

	/** @brief			录波文件列表处理类对象*/
	CDFRList* m_pCurDfrList;

	/** @brief			当前正在传输的文件信息*/
	FILE_TRANS_INFO m_CurTransFile;

	/** @brief			当前文件列表*/
	vector<DFR_FILE_INF> m_vCurFileList;

	/** @brief			待召唤文件列表*/
	vector<DFR_FILE_INF> m_vCallFileList;

	/** @brief			新文件列表*/
	vector<DFR_FILE_INF> m_vNewFileList;

	/** @brief          存储在本地的定值全路径*/
	string m_strLocalSGFile;

private:
	/**
	* @brief     		初始化各项配置
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitIED();

	/**
	* @brief     	初始化装置通信状态
	* @param[in]    无
	* @param[out]   无
	* @return       void
	*/
	void InitIedState();

	/**
	* @brief     	打开通讯端口
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL OpenCommun();

	/**
	* @brief     		初始化网络通讯
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitNetwork();

	/**
	* @brief     	    创建通讯对象, 负责创建通讯需要的通讯对象(总线或者点对点)
	* @param[out]       pDllPack 通讯库包装对象
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CreateCommTranObj(CSYCommunDllPack* pDllPack);

	/**
	* @brief     	    组装报文头
	* @param[in]        dwMsgType,报文类型
	* @param[in]        dwMsgLen，报文体长度
	* @return           void
	*/
	void AssemMsgHead( DWORD dwMsgType, DWORD dwMsgLen );

	/**
	* @brief     	接收录波器启动帧
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL RecvAndDealFirstFrame();
	
	/**
	* @brief     	    请求工作目录
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL AskWorkDirectory();

	/**  
	* @brief     	    请求录波器传送文件时的分包大小
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL AskPackSize(WORD& dwPackSize);

	/**
	* @brief     	收发报文
	* @param[in]    无
	* @param[out]   无
	* @return       int 0成功 -1发送失败 -2 接收失败
	*/
	int SendRecvData(int mode = RW_READ_WRITE);

	/**
	* @brief     		 发送报文
	* @param[in]     	 无
	* @param[out]    	 无
	* @return          	 int,0 成功 1失败
	*/
	int SendData();

	/**
	* @brief     	    从socket接收数据
	* @param[in]        无
	* @param[out]       无
	* @return           int，0成功 －1失败， －2 超时
	*/
	int RecvData();

	/**
	* @brief     	    处理收到的报文
	* @param[out]	    dwPType，报文类型
	* @return		    BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DealMsg(DWORD& dwPType);

	/**
	* @brief     		处理工作目录回复报文
	* @param[in]     	无
	* @param[out]    	无
	* @return         	BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DealWorkPathMsg();
	
	/**
	* @brief			处理请求传送文件分包大小结果报文
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DealPacketSizeMsg();

	/**
	* @brief     	    处理返回应答包
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DealReturnMsg();

	/**
	* @brief     	   从报文中分析报文类型和本帧报文DAT长度
	* @param[in]       dwMsgType,报文类型
	* @param[out]      dwLen,报文长度
	* @return          void
	*/
	void GetMsgTypeLen(DWORD& dwMsgType, DWORD& dwLen);

	/**
	* @brief     	   初始化发送缓冲区
	* @param[in]       无
	* @param[out]      无
	* @return          void
	*/
	void InitSendMsgBuff();

	/**			       
	* @brief           处理查询装置状态结果报文
	* @param[in]       无
	* @param[out]      无
	* @return          BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DealStateMsg();

	/**
	* @brief     	   获取LD属性结构指针
	* @param[in]       nLD，LD编号
	* @return          LD属性结构指针
	*/				   
	void GetLdAttr(int nLD, it_Ld& itor);

	/**
	* @brief     	   请求单个文件信息
	* @param[out]      chFilename，文件名
	* @param[in]       dwOffset,偏移量，默认为0，从头开始
	* @return          BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL AskSingleFile(const char* chFilename, DWORD dwOffset = 0);
	
	/**
	* @brief     	
	* @param[out]      stFileGet
	* @return          BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallSingleFileData(FILE_TRANS_INFO& stFileTrans);

	/**
	* @brief     	   处理装置回应的文件内容报文
	* @param[in]       无
	* @return          BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DealFileDataMsg( );

	/**
	* @brief     	   得到本地定值文件名（包含装置工作路径，但是处理了路径符号）
	* @param[in]       无
	* @param[out]      无
	* @return          string，本地的定值文件名
	*/
	string GetLocalSettingFileName();

	
	/**
	* @brief     	   分析定值文件，得到定值数据
	* @param[in]       无
	* @param[out]      无
	* @return          BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DealSettingFile();

	
	/**
	* @brief     	   从装置取最新文件列表
	* @param[in]	   无
	* @param[out]	   无
	* @return		   BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallRecentFileList();

	/**
	* @brief     	   处理RECENT.LST文件
	* @param[in]       无
	* @param[out]      无
	* @return          BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DealRecentListFile();

	
	/**
	* @brief     	   转换装置FAULT_INFO结构中的时间结构到MY_TIME_INFO结构(或反转)
	* @param[out]      stDataTime  DATE_TIME时间结构对象
	* @param[out]      stMyTimeInfo MY_TIME_INFO时间结构对象
	* @param[in]       flag	转换标志，默认为true: FAULT_INFO --> MY_TIME_INFO, false为反转
	* @return          void
	*/
	void CovertDATE_TIME2MY_TIME_INFO(DATE_TIME& stDataTime, MY_TIME_INFO& stMyTimeInfo, bool flag = true);

	/**
	* @brief     	 处理故障类型
	* @param[in]     dwFaulttype，故障类型
	* @param[out]    stDfrFileInf，波形文件结构
	* @param[out]    bTrip，是否是真实故障
	* @return        void
	*/
	void DealFaultType(DWORD dwFaulttype, DFR_FILE_INF& stDfrFileInf, bool& bTrip );

	/**
	* @brief     	 召唤一个COMTRADE文件（包含hdr\cfg\dat）
	* @param[in]     cFileName[256],文件名，不包含后缀
	* @return        BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallComtradeFile(string strFileName);

	/**
	* @brief     	 重新登录
	* @param[in]     无
	* @param[out]    无
	* @return        BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL Relogin();

	/**
	* @brief		发送新文件通知订单
	* @param[in]    dfr_itor 录波文件迭代器
	* @return		无
	*/
	void SendFileINF(vector<DFR_FILE_INF>::iterator dfr_itor);

	/**
	* @brief     	从绝对路径中得出文件名
	* @param[in]    strAbsPath 绝对路径
	* @return       std::string 不带路径的文件名
	*/
	std::string GetFileNameFromAbsPath( const string strAbsPath)
	{
		int nPathPos = strAbsPath.find_last_of('\\');
		if (nPathPos == string::npos)
		{
			nPathPos = strAbsPath.find_last_of('/');
		}

		//string strPath = strAbsPath.substr(0, nPathPos);
		string strFileNameNoPath = strAbsPath.substr(nPathPos+1);
		return strFileNameNoPath;
	}
};
/** @} */ //CCoIED OVER

#endif