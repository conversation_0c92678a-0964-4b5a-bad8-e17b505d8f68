#define the path of search
VPATH = -I./ -I../../nx_ga_common -I../../../../../nx_common -I../../../../../platform_include/plm_common -I../../../../../platform_include/plm_commun 
LABLE = 1.0.2

#cflags definition
_D=Y
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	MYLIB_PATH  = -L../../../../../nx_lib/release/
	OUT_PATH = ../../../../../nx_bin/release/ga/
	OBJ_PATH = ./release/
	OBJ_NAME = nx_ga_wld_com_103.so-$(LABLE)
else
	RUN_FLAGS = -g
	MYLIB_PATH  = -L../../../../../nx_lib/debug/
	OUT_PATH = ../../../../../nx_bin/debug/ga/
	OBJ_PATH = ./debug/
	OBJ_NAME = nx_ga_wld_com_103.so
endif

LIBS =  $(MYLIB_PATH) -L$(OUT_PATH) -lpthread -lsylib -ldl -rdynamic -lrt

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__

#object file definition
OBJS = $(OBJ_PATH)CoIED.o $(OBJ_PATH)Checkup.o $(OBJ_PATH)IED.o $(OBJ_PATH)ga_comtrade.o $(OBJ_PATH)Co103IED.o \
$(OBJ_PATH)SYCommunDllPack.o $(OBJ_PATH)ga_functions.o $(OBJ_PATH)I_IED_Run.o \
$(OBJ_PATH)CoDirect.o $(OBJ_PATH)GADirect.o $(OBJ_PATH)FailReason.o $(OBJ_PATH)ga_dfrlist.o \
$(OBJ_PATH)CsgLogRecord.o $(OBJ_PATH)CsgLogRecordMngr.o


$(OBJ_NAME) : $(OBJS) mksylibdir
	g++ -o  $(OUT_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -shared -fpic

$(OBJ_PATH)CoIED.o : ./CoIED.cpp mkobjdir
	g++ -o $(OBJ_PATH)CoIED.o $(CFLAGS) $(VPATH) \
	-c ./CoIED.cpp

$(OBJ_PATH)Checkup.o : ../../nx_ga_common/Checkup.cpp
	g++ -o $(OBJ_PATH)Checkup.o $(CFLAGS) $(VPATH) \
	    -c ../../nx_ga_common/Checkup.cpp

$(OBJ_PATH)IED.o : ../../nx_ga_common/IED.cpp
	g++ -o $(OBJ_PATH)IED.o  $(CFLAGS) $(VPATH) \
	    -c  ../../nx_ga_common/IED.cpp 
	
$(OBJ_PATH)ga_comtrade.o : ../../nx_ga_common/ga_comtrade.cpp
	g++ -o $(OBJ_PATH)ga_comtrade.o  $(CFLAGS) $(VPATH) \
	-c ../../nx_ga_common/ga_comtrade.cpp

$(OBJ_PATH)Co103IED.o : ../../nx_ga_common/Co103IED.cpp
	g++ -o $(OBJ_PATH)Co103IED.o $(CFLAGS) $(VPATH) \
	-c ../../nx_ga_common/Co103IED.cpp 

$(OBJ_PATH)SYCommunDllPack.o : ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp
	g++ -o $(OBJ_PATH)SYCommunDllPack.o $(CFLAGS) $(VPATH) \
	-c ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp 

$(OBJ_PATH)ga_functions.o : ../../nx_ga_common/ga_functions.cpp
	g++ -o $(OBJ_PATH)ga_functions.o $(CFLAGS) $(VPATH) \
	-c ../../nx_ga_common/ga_functions.cpp 

$(OBJ_PATH)I_IED_Run.o : ../../nx_ga_common/I_IED_Run.cpp
	g++ -o $(OBJ_PATH)I_IED_Run.o $(CFLAGS) $(VPATH) \
	-c ../../nx_ga_common/I_IED_Run.cpp

$(OBJ_PATH)CoDirect.o : ./CoDirect.cpp
	g++ -o $(OBJ_PATH)CoDirect.o $(CFLAGS) $(VPATH) \
	-c ./CoDirect.cpp

$(OBJ_PATH)GADirect.o : ../../nx_ga_common/GADirect.cpp
	g++ -o $(OBJ_PATH)GADirect.o $(CFLAGS) $(VPATH) \
	-c ../../nx_ga_common/GADirect.cpp

$(OBJ_PATH)FailReason.o : ../../nx_ga_common/FailReason.cpp
	g++ -o $(OBJ_PATH)FailReason.o $(CFLAGS) $(VPATH) \
	-c ../../nx_ga_common/FailReason.cpp
	
$(OBJ_PATH)ga_dfrlist.o : ../../nx_ga_common/ga_dfrlist.cpp
	g++ -o $(OBJ_PATH)ga_dfrlist.o $(CFLAGS) $(VPATH) \
	-c ../../nx_ga_common/ga_dfrlist.cpp

$(OBJ_PATH)CsgLogRecord.o : ../../../../../nx_common/CsgLogRecord.cpp
	g++ -o $(OBJ_PATH)CsgLogRecord.o $(CFLAGS) $(VPATH) \
	-c ../../../../../nx_common/CsgLogRecord.cpp
	
$(OBJ_PATH)CsgLogRecordMngr.o : ../../../../../nx_common/CsgLogRecordMngr.cpp
	g++ -o $(OBJ_PATH)CsgLogRecordMngr.o $(CFLAGS) $(VPATH) \
	-c ../../../../../nx_common/CsgLogRecordMngr.cpp
	
mkobjdir:
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists;";   else mkdir -p $(OBJ_PATH); fi

mksylibdir:
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists";   else mkdir -p $(OUT_PATH); fi
	
.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OUT_PATH)$(OBJ_NAME) $(OBJS)
