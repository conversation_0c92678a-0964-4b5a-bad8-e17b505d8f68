#include "CoDirect.h"


CCoDirect::CCoDirect(void)
{
}


CCoDirect::~CCoDirect(void)
{
}

CIED* CCoDirect::CreateIED(FACTORY_ATBUTE* pFactory, IED_ATBUTE* pIED, CLogRecord*pIEDLog)
{
	CIED* pIedInstance = NULL;

	if (pIED->base_inf.device_type.compare("LFP901A")==0)
	{
		CLFP901A* pCoIED = new CLFP901A(pFactory, pIED, pIEDLog);
		pIedInstance = (CIED*)pCoIED;
	}
	else if (pIED->base_inf.device_type.compare("LFP902A")==0)
	{
		CLFP902A* pCoIED = new CLFP902A(pFactory, pIED, pIEDLog);
		pIedInstance = (CIED*)pCoIED;
	}
	else if (pIED->base_inf.device_type.compare("LFP902C")==0)
	{
		CLFP902C* pCoIED = new CLFP902C(pFactory, pIED, pIEDLog);
		pIedInstance = (CIED*)pCoIED;
	}
	else if (pIED->base_inf.device_type.compare("LFP923A")==0)
	{
		CLFP923A* pCoIED = new CLFP923A(pFactory, pIED, pIEDLog);
		pIedInstance = (CIED*)pCoIED;
	}
	else if (pIED->base_inf.device_type.compare("LFP941A")==0)
	{
		CLFP941A* pCoIED = new CLFP941A(pFactory, pIED, pIEDLog);
		pIedInstance = (CIED*)pCoIED;
	}
	else
	{
		CLFPBase* pCoIED = new CLFPBase(pFactory, pIED, pIEDLog);
		pIedInstance = (CIED*)pCoIED;
	}

	return pIedInstance;
}