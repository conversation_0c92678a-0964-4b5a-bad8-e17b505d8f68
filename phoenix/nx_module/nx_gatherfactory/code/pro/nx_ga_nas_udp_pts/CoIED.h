﻿/****************************************************************
*  CoIED.h       author: gcy      date: 05/09/2013
*----------------------------------------------------------------
*  note: 实现 思唯奇网络存储器 规约类
*  
*****************************************************************/

#ifndef _COIED_H_
#define _COIED_H_

#include <bitset>
#include "IED.h"
#include "FindSortFuns.h"
#include "ga_comtrade.h"

/** @brief   CPU属性迭代器*/
typedef vector<LD_ATBUTE>::iterator it_Ld;

/**
*@defgroup   思唯奇网络存储器采集规约类
*@{
*/

/**
*@brief      实现思唯奇网络存储器数据采集
*<AUTHOR>
date:        09/09/2013
*
*example
*@code*
*    
*    
*    
*    
@endcode
*/
#pragma once
class CCoIED : public CIED
{
public:
	CCoIED(void);

	/**
	* @brief     		构造函数
	* @param[out]		pFactory，采集工厂属性指针
	* @param[out]		p_st_Ied, IED属性指针
	* @param[out]		pCoIEDLog，日志属性指针
	* @return			无
	*/
	CCoIED(FACTORY_ATBUTE* pFactory, IED_ATBUTE* p_st_Ied, CLogRecord* pCoIEDLog);

	~CCoIED(void);

	/**
	* @brief     		初始化各项配置
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitIED();

	/**
	* @brief     	    创建通讯对象, 负责创建通讯需要的通讯对象(总线或者点对点)
	* @param[out]       pDllPack 通讯库包装对象
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CreateCommTranObj(CSYCommunDllPack* pDllPack);

	/**
	* @brief			召唤通讯状态
	* @param[in]		
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallAllLDState();

	/**
	* @brief			召唤指定LD的定值
	* @param[in]		nld,LD编号
	* @param[in]		zone,定值区号: -1:表示召唤当前区定值
	* @return			TRUE：成功， FALSE：失败                       
	*/
	BOOL CallSG(int nld, int zone);

	BOOL CallAllLDAction();
	
	BOOL ReadData();

	/**
	* @brief     	 重新登录
	* @param[in]     无
	* @param[out]    无
	* @return        BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL Relogin();

private:
	/** @brief       当前cpu*/
	u_int8 m_nCurCpu;

	/** @brief		接收UDP报文失败次数*/
	u_int8 m_nErrRecvNo;

private:
	/**
	* @brief     	打开通讯端口
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL OpenCommun();

	/**
	* @brief     	检验报文
	* @param[in]    无
	* @param[out]   无
	* @return       bool,true:成功,false:失败
	*/
	bool CheckUp();

	BOOL DealMsg( );
};
/** @} */ //CCoIED OVER

#endif