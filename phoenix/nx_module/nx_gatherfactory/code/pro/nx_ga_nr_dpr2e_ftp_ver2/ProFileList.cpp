﻿#pragma warning(disable:4786)
#pragma warning(disable:4275)

#include "ProFileList.h"

CProFileList::CProFileList()
{

}

CProFileList::~CProFileList()
{

}

void CProFileList::init(list_file_info* ftpfileinfo, u_int16 nIed, CLogRecord* pIEDLog)
{
	m_dev_ip = ftpfileinfo->dev_ip;
	m_user_name = ftpfileinfo->user_name;
	m_password = ftpfileinfo->password;

	m_src_file = ftpfileinfo->src_file;
	m_des_file = ftpfileinfo->des_file;
	m_src_path = ftpfileinfo->src_path;

	m_ied_id = nIed;
	m_pIEDLog = pIEDLog;
}

bool CProFileList::get_file_list(netbuf* control, vector<DFR_FILE_INF> &vec_file_list)
{
	if (get_list_from_file(control, vec_file_list))
	{
		return true;
	}

	/************************************************************************/
	/* 如果通过列表文件读取失败，则直接访问文件夹                           */
	/************************************************************************/

	if (FtpDir(m_des_file.c_str(), m_src_path.c_str(), control)!=1)
	{
		return false;
	}

	if (!get_list_from_path(vec_file_list))
	{
		return false;
	}

	return true;
}


bool CProFileList::get_list_from_path(vector<DFR_FILE_INF> &vec_file_list)
{
	vec_file_list.clear();
	
	if (sy_dirfile_exist(m_des_file.c_str())!=0)
	{
		return false;
	}

	char str1[100];
	fault_file_info m_ffi;//列表中没有文件大小？
	memset(str1,0,sizeof(str1));
	FILE *fp;
	fp = NULL;
	memset(&m_ffi,0,sizeof(m_ffi));
	string tmp_str="";

	struct tm *local;

	fp=fopen("faultlist.dpr","rb");

	short cur_fault_num;
	short cur_fault_circle;
	int f_ptr=0;
	if (fp == NULL)
	{
		exit(-1);
	}

	fread(&cur_fault_num,1,sizeof( short),fp); 
	fread(&cur_fault_circle,1,sizeof( short),fp); 
	MY_TIME_INFO systime;
	for(int i=0;i<cur_fault_num;i++)
	{
		tmp_str="";

		f_ptr=((cur_fault_num-i-1+1000)%1000)*sizeof( fault_file_info)+8*sizeof( short);

		fseek(fp,f_ptr,SEEK_SET);
		fread(&m_ffi,1,sizeof( fault_file_info),fp);

		local = localtime(&m_ffi.recordTime);
		if(local != NULL)
		{
			sprintf(str1,"%s_%s_%s_%s_%s_%s",local->tm_year + 1900,local->tm_mon + 1 ,local->tm_mday,local->tm_hour,local->tm_min,local->tm_sec);			
		}
		else
		{
			char stringTemp[6][5];
			memset(stringTemp,0,sizeof(stringTemp));
			int cur_count =14;
			memcpy(&stringTemp[0][0],&m_ffi.fileName[cur_count],4);
			stringTemp[0][4] = '\0';
			cur_count = cur_count+4;
			memcpy(&stringTemp[1][0],&m_ffi.fileName[cur_count],2);
			stringTemp[1][2] = '\0';
			cur_count = cur_count+2;
			memcpy(&stringTemp[2][0],&m_ffi.fileName[cur_count],2);
			stringTemp[2][4] = '\0';
			cur_count = cur_count+2;
			memcpy(&stringTemp[3][0],&m_ffi.fileName[cur_count],2);
			stringTemp[3][4] = '\0';
			cur_count = cur_count+3;
			memcpy(&stringTemp[4][0],&m_ffi.fileName[cur_count],2);
			stringTemp[4][4] = '\0';
			cur_count = cur_count+2;
			memcpy(&stringTemp[5][0],&m_ffi.fileName[cur_count],2);
			stringTemp[5][4] = '\0';

			sprintf(str1,"%s_%s_%s_%s_%s_%s",stringTemp[0],stringTemp[1],stringTemp[2],stringTemp[3],stringTemp[4],stringTemp[5]);

		}
		string str_file_info(str1);
		systime.nYear = atoi(str_file_info.substr(0, 4).c_str());
		systime.nMon = atoi(str_file_info.substr(5, 2).c_str());
		systime.nDay = atoi(str_file_info.substr(8, 2).c_str());
		systime.nHour = atoi(str_file_info.substr(11, 2).c_str());
		systime.nMin = atoi(str_file_info.substr(14, 2).c_str());
		systime.nSec = atoi(str_file_info.substr(17, 2).c_str());

		bool bfind = false;
		for (vector<DFR_FILE_INF>::iterator iter=vec_file_list.begin(); iter!=vec_file_list.end(); iter++)
		{
			if ( iter->strFileName == m_ffi.fileName )
			{
				bfind = true;
				break;
			}
		}

		if (!bfind)
		{
			DFR_FILE_INF wave_file;
			wave_file.tmFaultTime = systime;
			//wave_file.strFileName = m_ffi.fileName;
			char cFilePath[256];
			char cFileName[256];
			char cFileExt[256];
			sy_get_file_name(m_ffi.fileName, cFilePath, cFileName, cFileExt);
			wave_file.strFileName = cFileName;
			wave_file.strFilePath = cFilePath;
			vec_file_list.push_back(wave_file);
		}
	}

	fclose(fp);

	return true;

}

bool CProFileList::get_list_from_file(netbuf* control, vector<DFR_FILE_INF> &vec_file_list)
{
	if (!get_file(control))
	{
		return false;
	}

	if (!get_list(vec_file_list))
	{
		return false;
	}

	return true;
}

bool CProFileList::get_file(netbuf* control)
{
	if (!get_ftp_status(control))
	{
		return false;
	}
		
	if (sy_dirfile_exist(m_des_file.c_str())==0)
	{
		remove(m_des_file.c_str());
	}
	
	if (!FtpGet(m_des_file.c_str(), m_src_file.c_str(), 'I', control)==1)
	{
		m_pIEDLog->FormatLogOut(CLogRecord::errorlog, "装置%u 从FTP拷贝文件[%s]到本地[%s]失败,原因：[%s]！", 
			m_ied_id, m_src_file.c_str(), m_des_file.c_str(), control->response);
		return false;
	}

	return true;

}

bool CProFileList::get_ftp_status(netbuf* control)
{
	char str_path[max_len];
	memset(str_path, 0, max_len);
		
	if(control != NULL)
	{
		if (FtpPwd(str_path, max_len, control))	
		{
			return true;
		}

		FtpQuit(control);
		control = NULL;
	}
		
	if (FtpConnect(m_dev_ip.c_str(), &control))
	{	
		if (FtpLogin(m_user_name.c_str(),m_password.c_str(), control))
		{
			return true;
		}
	}
				
	if (control != NULL)
	{
		FtpQuit(control);
		control=NULL;
	}
		
	return false;
}

bool CProFileList::get_list(vector<DFR_FILE_INF> &vec_file_list)
{
	vec_file_list.clear();

	if (sy_dirfile_exist(m_des_file.c_str())!=0)
	{
		return false;
	}

// 	ifstream idx_file(m_des_file.c_str());
// 	while (!idx_file.eof()) 
// 	{
// 		char temp[1024]; 
// 		idx_file.getline(temp, 1024, '\n');
// 
// 		if (strcmp(temp, "")==0)
// 		{
// 			continue;
// 		}
// 
// 		char file_path[256]="";
// 		char file_name[256]="";
// 		MY_TIME_INFO systime;
// 
// 		DFR_FILE_INF wave_file;
// 		//这里取的文件大小只是dat文件的大小(KB)。
// 		get_file_info(temp, file_path, file_name, systime, wave_file.nFileSize);
// 		
// 		wave_file.strFileName =  file_name;
// 		wave_file.tmFaultTime = systime;
// 		vec_file_list.push_back(wave_file);
// 	}
// 	idx_file.close();

	char str1[100];
	fault_file_info m_ffi;//列表中没有文件大小？
	memset(str1,0,sizeof(str1));
	FILE *fp;
	fp = NULL;
	memset(&m_ffi,0,sizeof(m_ffi));
	string tmp_str="";

	struct tm *local;

	fp=fopen("faultlist.dpr","rb");

	short cur_fault_num;
	short cur_fault_circle;
	int f_ptr=0;
	if (fp == NULL)
	{
		exit(-1);
	}

	fread(&cur_fault_num,1,sizeof( short),fp); 
	fread(&cur_fault_circle,1,sizeof( short),fp); 
	MY_TIME_INFO systime;
	for(int i=0;i<cur_fault_num;i++)
	{
		tmp_str="";

		f_ptr=((cur_fault_num-i-1+1000)%1000)*sizeof( fault_file_info)+8*sizeof( short);

		fseek(fp,f_ptr,SEEK_SET);
		fread(&m_ffi,1,sizeof( fault_file_info),fp);

		local = localtime(&m_ffi.recordTime);

		if(local != NULL)
		{
			sprintf(str1,"%s_%s_%s_%s_%s_%s",local->tm_year + 1900,local->tm_mon + 1 ,local->tm_mday,local->tm_hour,local->tm_min,local->tm_sec);			
		}
		else
		{
			char stringTemp[6][5];
			memset(stringTemp,0,sizeof(stringTemp));
			int cur_count =14;
			memcpy(&stringTemp[0][0],&m_ffi.fileName[cur_count],4);
			stringTemp[0][4] = '\0';
			cur_count = cur_count+4;
			memcpy(&stringTemp[1][0],&m_ffi.fileName[cur_count],2);
			stringTemp[1][2] = '\0';
			cur_count = cur_count+2;
			memcpy(&stringTemp[2][0],&m_ffi.fileName[cur_count],2);
			stringTemp[2][4] = '\0';
			cur_count = cur_count+2;
			memcpy(&stringTemp[3][0],&m_ffi.fileName[cur_count],2);
			stringTemp[3][4] = '\0';
			cur_count = cur_count+3;
			memcpy(&stringTemp[4][0],&m_ffi.fileName[cur_count],2);
			stringTemp[4][4] = '\0';
			cur_count = cur_count+2;
			memcpy(&stringTemp[5][0],&m_ffi.fileName[cur_count],2);
			stringTemp[5][4] = '\0';

			sprintf(str1,"%s_%s_%s_%s_%s_%s",stringTemp[0],stringTemp[1],stringTemp[2],stringTemp[3],stringTemp[4],stringTemp[5]);

		}

		DFR_FILE_INF wave_file;
		string str_file_info(str1);
		systime.nYear = atoi(str_file_info.substr(0, 4).c_str());
		systime.nMon = atoi(str_file_info.substr(5, 2).c_str());
		systime.nDay = atoi(str_file_info.substr(8, 2).c_str());
		systime.nHour = atoi(str_file_info.substr(11, 2).c_str());
		systime.nMin = atoi(str_file_info.substr(14, 2).c_str());
		systime.nSec = atoi(str_file_info.substr(17, 2).c_str());

		wave_file.tmFaultTime = systime;
		//wave_file.strFileName = m_ffi.fileName;
		char cFilePath[256];
		char cFileName[256];
		char cFileExt[256];
		sy_get_file_name(m_ffi.fileName, cFilePath, cFileName, cFileExt);
		wave_file.strFileName = cFileName;
		wave_file.strFilePath = cFilePath;
		vec_file_list.push_back(wave_file);
	}

	fclose(fp);

	return true;
}

// void CProFileList::get_file_info(char* file_info, char* file_path, char* file_name, MY_TIME_INFO &systime, int& nDatFileSize)
// {
// 	//2011-12-13 19:49:18.432 1305187 42 220kV鹿莎线电流B相电流 突变量 - \data\DPU1JOR1061.dat
// 
// 	string str_file_info(file_info);
// 
// 	systime.nYear = atoi(str_file_info.substr(0, 4).c_str());
// 	systime.nMon = atoi(str_file_info.substr(5, 2).c_str());
// 	systime.nDay = atoi(str_file_info.substr(8, 2).c_str());
// 	systime.nHour = atoi(str_file_info.substr(11, 2).c_str());
// 	systime.nMin = atoi(str_file_info.substr(14, 2).c_str());
// 	systime.nSec = atoi(str_file_info.substr(17, 2).c_str());
// 	//systime.nMilSec = atoi(str_file_info.substr(20, 3).c_str());
// 
// 	//得到dat文件大小
// 	int nSpacePos = str_file_info.find(" ", 24);
// 	if (nSpacePos != string::npos)
// 	{
// 		nDatFileSize = atoi(str_file_info.substr(24, nSpacePos-24).c_str());
// 		nDatFileSize = nDatFileSize/1024;
// 	}
// 	else
// 	{
// 		nDatFileSize = 0;
// 	}
// 	
// 	string str_temp = str_file_info.substr(str_file_info.find("\\"));
// 
// 	string str_path = str_temp.substr(0, str_temp.rfind("\\")+1);
// 	strcpy(file_path, str_path.c_str());
// 
// 	str_temp = str_temp.substr(str_temp.rfind("\\")+1);
// 	string str_name = str_temp.substr(0, str_temp.rfind("."));
// 	strcpy(file_name, str_name.c_str());
// }















