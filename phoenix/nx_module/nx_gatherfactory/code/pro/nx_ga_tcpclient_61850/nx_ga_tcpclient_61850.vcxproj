﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CB4F134C-D94E-4024-A316-91BF0453E970}</ProjectGuid>
    <RootNamespace>nx_ga_tcpclient_61850</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>..\..\..\..\..\nx_bin\debug\ga\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IntDir>$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>..\..\..\..\..\nx_bin\release\ga\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IntDir>$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_USE_32BIT_TIME_T;_WINDLL;_MBCS;_WIN32;_WINDOWS;__PLATFORM_MS_WIN__;MMS_LITE;ETHERNET;DEBUG_SISCO;MOSI;LEAN_T;MVL_UCA</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>./;../../nx_ga_common;../../../../../nx_common;../../../../../platform_include/plm_common;../../../../../platform_include/plm_commun;../../../../../platform_include/external/cosmos/inc;../../../../../platform_include/external/cosmos/mmslite/inc;../../../../../platform_include/external/;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;acsi_ld.lib;mmslite_ld.lib;xerces-c_3D.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\..\..\..\nx_lib\debug\;../../../../../platform_include/external/cosmos/lib/win32;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../nx_ga_common;../../../../../nx_common;../../../../../platform_include/plm_common;../../../../../platform_include/plm_commun;../../../../../platform_include/external/cosmos/inc;../../../../../platform_include/external/cosmos/mmslite/inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_USE_32BIT_TIME_T;_WINDLL;_MBCS;_WIN32;_WINDOWS;__PLATFORM_MS_WIN__;MMS_LITE;ETHERNET;DEBUG_SISCO;MOSI;LEAN_T;MVL_UCA</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>..\..\..\..\..\nx_lib\release\;../../../../../platform_include/external/cosmos/lib/win32</AdditionalLibraryDirectories>
      <AdditionalDependencies>sylib.lib;acsi_l.lib;mmslite_l.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\UnTime.cpp" />
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp" />
    <ClCompile Include="..\..\nx_ga_common\Checkup.cpp" />
    <ClCompile Include="..\..\nx_ga_common\FailReason.cpp" />
    <ClCompile Include="..\..\nx_ga_common\GADirect.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ga_comtrade.cpp" />
    <ClCompile Include="..\..\nx_ga_common\IED.cpp" />
    <ClCompile Include="..\..\nx_ga_common\I_IED_Run.cpp" />
    <ClCompile Include="..\..\nx_ga_common\ThreadManger.cpp" />
    <ClCompile Include="..\..\nx_ga_factory\GAOper.cpp" />
    <ClCompile Include="CallBackFunc.cpp" />
    <ClCompile Include="CoDirect.cpp" />
    <ClCompile Include="CoIED.cpp" />
    <ClCompile Include="nx_ga_tcpclient_61850_modify_note.cpp" />
    <ClCompile Include="tinystr.cpp" />
    <ClCompile Include="tinyxml.cpp" />
    <ClCompile Include="tinyxmlerror.cpp" />
    <ClCompile Include="tinyxmlparser.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\nx_common\UnTime.h" />
    <ClInclude Include="..\..\nx_ga_common\Checkup.h" />
    <ClInclude Include="CallBackFunc.h" />
    <ClInclude Include="CoDirect.h" />
    <ClInclude Include="CoIED.h" />
    <ClInclude Include="iec61850define.h" />
    <ClInclude Include="ParseScdIedIP.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="tinystr.h" />
    <ClInclude Include="tinyxml.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ga_tcpclient_61850.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>