<?xml version="1.0" encoding="utf-8"?>
<!--Ver 1.0.4.1 -->
<!--
配置文件制作说明:
	节点说明:
		label节点：描述配置文件修改记录等描述性内容
		table节点:表示某个表
		field节点:表示某个字段
	
	属性说明:
		name:表示表或字段的名称,如果字段是数据库的关键字或数字开头,必须进行特殊处理,否则不能存库
		des:表示表或字段的描述
		range:表示范围,用于下拉框的内容填充,格式如:range="1:ASCII格式;2:BINARY格式"
		mintomax:表示数字范围,格式如:mintomax="100:500"
		isFK:如果是外键,则填入:表名称.主键
		isEdit:如果该字段不需要界面修改,则需要isEdit="n"
		length:字段长度,每个字段必须设定
		isNull:是否为空,如果不为空,则需要设定isNull="n"
		note:某字段的扩展说明，比如note=周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭
			 table中的note属性的内容是关于对该表配置的额外说明，需要界面显示的提示（即在明显的位置放置提示Lable）给配点表人员！
	
	实例化说明：
		1.具体规约在实例话时请将用不到的表或表中某些字段删除！删除某表中的某字段时若该字段被标明非NULL（即isNull="n"）的，不可删除！
		2.如需要指明某字段的具体含义，可修改其desc和note属性，并给出默认值
-->

<GaConfig des="采集工厂配置">
	<label>
		<!--适用规约-->
		<ga_pro>银山故障录波器规约</ga_pro>
	
		<!--文档版本版本说明,描述文档的变更情况-->
		<version des="版本修改履历">
			<update>
				<date des="日期">2015-01-12</date>
				<person des="人员">赵健</person>
				<reason des="原因">首次编写该文档！</reason>
			</update>
		</version> 
		
		<!--装置端特殊配置说明,描述装置端的配置-->
		<devcfg  des="装置端规约配置">
			<cfg des="配置">
				<step des="步骤"></step>
			</cfg>
		</devcfg>
		 
		<!--常见问题说明,描述常见的现场问题-->
		<problems des="现场常见问题">
			<problem des="问题1">
				<description des="描述问题1"></description>
				<scheme des="解决方案"></scheme>
			</problem>
		</problems>
	</label>
	
	<tables> 
		<!--采集工厂表配置 -->
		<type name="ga_factory">
			<table name="nx_t_factory_config" desc="采集工厂配置信息表">

				<fields>
					<field name="obj_id" desc="采集口编号" mintomax="1:2000">3</field>
					<field name="aliasname"  desc="采集口名称" length="64">NET3</field>
					<field name="psrtype"  desc="网络UDP" >6</field>
					<field name="protocol_obj"  desc="对应的规约" isFK="nx_t_protocol_cfg.obj_id">1</field>
					<field name="station_obj"  desc="厂站编号" isFK="nx_t_substation.obj_id">1</field>
					<field name="rev_timeout"  desc="接收超时">2000</field>
					<field name="send_timeout"  desc="发送超时">200</field>
					<field name="pmsip_a"  desc="本机IP地址" length="32">***********50</field>
					<field name="pmsipport_a"  desc="本机端口">5101</field>
					<field name="dataouttag"  desc="报文输出标记" range="0:不输出;1:按装置输出到多个文件;2:其他：全部输出到一个文件" note="调试时建议输出日志和报文">1</field>
					<field name="datalen"  desc="报文输出长度(大帧报文输出最大长度，单位:字节)">2560</field>
					<field name="savedatatype"  desc="保存报文类型" range="1:BINARY格式;2:ASCII格式">1</field>
					<field name="logout_tag"  desc="日志输出标记" range="0:不输出;1:按装置输出到多个文件;2:其他全部输出到一个文件">1</field>
					<field name="log_level"  desc="日志级别" range="1:仅记录错误日志;2:记录错误及跟踪类所有日志;3:记录所有相关日志">3</field>
					<field name="rcd_days"  desc="日志记录天数(日志及报文记录的总天数.0:表示不记录日志及报文)">3</field>
					<field name="print2screen"  desc="日志、报文是否输出到屏幕" range="1:输出;0:不输出">1</field>
				</fields>

				<fields>
					<field name="obj_id" desc="采集口编号" mintomax="1:2000">3</field>
					<field name="aliasname"  desc="采集口名称" length="64">NET3</field>
					<field name="psrtype"  desc="网络TCP" >4</field>
					<field name="protocol_obj"  desc="对应的规约" isFK="nx_t_protocol_cfg.obj_id">1</field>
					<field name="station_obj"  desc="厂站编号" isFK="nx_t_substation.obj_id">1</field>
					<field name="rev_timeout"  desc="接收超时">2000</field>
					<field name="send_timeout"  desc="发送超时">200</field>
					<field name="pmsip_a"  desc="本机IP地址" length="32">***********50</field>
					<field name="pmsipport_a"  desc="本机端口">5101</field>
					<field name="dataouttag"  desc="报文输出标记" range="0:不输出;1:按装置输出到多个文件;2:其他：全部输出到一个文件" note="调试时建议输出日志和报文">1</field>
					<field name="datalen"  desc="报文输出长度(大帧报文输出最大长度，单位:字节)">2560</field>
					<field name="savedatatype"  desc="保存报文类型" range="1:BINARY格式;2:ASCII格式">1</field>
					<field name="logout_tag"  desc="日志输出标记" range="0:不输出;1:按装置输出到多个文件;2:其他全部输出到一个文件">1</field>
					<field name="log_level"  desc="日志级别" range="1:仅记录错误日志;2:记录错误及跟踪类所有日志;3:记录所有相关日志">3</field>
					<field name="rcd_days"  desc="日志记录天数(日志及报文记录的总天数.0:表示不记录日志及报文)">3</field>
					<field name="print2screen"  desc="日志、报文是否输出到屏幕" range="1:输出;0:不输出">1</field>
				</fields>

			</table>
			
			<!--采集规约动态库配置 -->
			<table name="nx_t_protocol_cfg" desc="通讯规约配置表">
				<field name="obj_id" desc="规约编号" mintomax="1:9999">1</field>
				<field name="aliasname" desc="规约名称">银山故障录波器规约</field>
				<field name="prodllname"  desc="动态库名称(不带文件后缀)">nx_ga_ys_tcpclient_ys89a</field>
				<!--这里必须为1-->
				<field name="workrole"  desc="规约角色(1:IED接入  2：信息远传)" isEdit="n">1</field>
				<field name="manufacturer"  desc="厂家（直接填写厂家中文名称，禁止空格">南京银山</field>
			</table>
			
		  <table name="nx_t_factory_cyc_time_config" desc="采集工厂自动轮询周期信息表">
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">1</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询动作周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">2</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询告警周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
			
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">3</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询开关量周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
								
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">4</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询定值周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">5</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询模拟量周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">6</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询软压板周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">7</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询当前定值区号周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">8</field>
					<field name="aliasname" desc="周期名称" isEdit="n">时钟同步周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">9</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询新文件周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">300</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>				
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">10</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询通讯状态周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">61</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>	
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">11</field>
					<field name="aliasname" desc="周期名称" isEdit="n">重新登陆周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">67</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">12</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询录波列表周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>	
				
			</table>
		</type>
		
		<!-- IED配置-->
		<type name="IED_config">
			<table name="nx_t_ied" desc="智能设备基本信息表">
				<field name="obj_id" desc="IED编号" note="从1开始编写">1</field>
				<field name="aliasname"  desc="设备名称" length="64" isNull="n">故障录波器YS89A</field>
				<field name="abbrename"  desc="IED名称缩写" length="64">YS89A</field>
				<field name="station_obj"  desc="所属变电站" isFK="nx_t_substation.obj_id">1</field>
				<field name="primequ_obj"  desc="关联一次设备" isFK="nx_t_primequipment.obj_id">1</field>
				<field name="opramode"  desc="当前运行状态" range="0:检修;1:停运;2:投运;3:未接入;4:调试(对码表);5:其它，未知">2</field>
				<field name="model"  desc="型号" isFK="nx_t_ied_type.ied_type" length="64">YS89A</field>
				<field name="psrtype"  desc="类型" range="-1:其它编号,未知;0:子站;1:线路保护;2:变压器保护;3:母差保护;4:母联保护;5:发变组保护;6:开关保护;7:电容器保护;8:电抗器保护;10:故障录波器;11:网络报文记录仪;12:测控装置;13:电能表;14:安稳装置;15:低周减载装置;16:低频解列装置;17:GPS装置;18:合并单元;19:智能终端;20:交换机;21:路由器">10</field>
				<field name="addr"  desc="站内设备地址" note="站内地址，用于数据采集，不大于255" mintomax="1:255">1</field>
				<field name="factory_obj"  desc="采集口编号" isFK="nx_t_factory_config.obj_id">1</field>
				<field name="ipaddr_a"  desc="A网IP地址" length="32">***********</field>
				<field name="ipport_a"  desc="A网端口">1518</field>	
				<field name="ipaddr_b"  desc="B网IP地址" length="32">***********</field>
				<field name="ipport_b"  desc="B网端口">1050</field>
	    </table>
			
		   <!-- CPU配置-->
			<table name="nx_t_ied_ld" desc="IED逻辑设备(CPU)表"> 
				<fields>
					<field name="ld_code" desc="ld的编号" note="要求同一装置编号不相同,如果装置不是多CPU，默认建1个LD" isNull="n">1</field>
					<field name="ied_obj" desc="所属IED" isPK="nx_t_ied.obj_id"></field>
					<field name="aliasname"  desc="逻辑设备名称" length="64">CPU1</field>
					<field name="ldversion"  desc="版本" length="64"></field>
					<field name="chkcode"  desc="校验码"  length="32"></field>
					<field name="ldfun"  desc="功能号" note="用于采集模块录波文件召唤，和CPU完成的保护功能相关">0</field>
					<field name="objpathname"  desc="对象路径"  length="256" note="对象路径，如果用于61850，则为装置IED的name+ldinst；原则上一个ld对应一个记录；"></field>
					<field name="strbackup1"  desc="字符备用1" length="32"></field>
					<field name="strbackup2"  desc="字符备用2" length="32"></field>
					<field name="strbackup3"  desc="字符备用3" length="32"></field>
				</fields>
			</table>
			
			<!-- 设备扩展配置-->
			<table name="nx_t_factory_ied_config" desc="采集工厂对二次设备的访问处理配置表">
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id"></field>
				<field name="user" desc="装置用户名" length="64"></field>
				<field name="pswd" desc="装置密码" length="64"></field>
				<field name="callfile_flag" desc="召唤录波方式" range="0:不召唤录波文件;1:召唤所有的录波;2:召唤有故障的录波">1</field>
				<field name="precycles"  desc="故障前预录的周波数" >2</field>
				<field name="prefreq"  desc="故障前预录的频率" >50</field>
				<field name="cmdflag"  desc="强制召唤标志" range="0:不强制召唤;1:强制召唤">0</field>
				<field name="calltimes"  desc="重发报文次数" note="命名失败后，重发报文次数">3</field>
				<field name="calldata1times"  desc="重发一级报文次数" note="提示没有一级数据后，重发召唤一级数据次数">1</field>
				<field name="param1" desc="召唤录波文件天数" length="64" isEdit="n">1</field>
				<field name="param2" desc="每次召唤文件个数" isEdit="n" length="64">50</field>
				<field name="param3" desc="CFG文件中月日是否反向" isEdit="n" length="64">0</field>
				<field name="param4" desc="定值文件名" isEdit="n" length="64">c:\\ysparam\\param.0001</field>
		  </table> 		  
		</type>
		<!-- IED配置结束-->
		
		<!-- 以下是点表配置 -->
		<type name="LD_config"> 
			
			<!--定值配置表-->
			<table name="nx_t_ied_sg_cfg" desc="IED逻辑设备下的定值配置表" note="这里写对该表需要特殊说明的地方！">
				<field name="sg_code" desc="定值编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="定值名称" length="128">none</field>
				<field name="abbrename" desc="定值代码" length="64" note="定值名称缩写">none</field>
				<field name="holdattr_1" desc="定值编号" length="32">none</field>
			</table> 

		<!-- 点表配置结束！ -->
	</type>
  </tables>
 </GaConfig> 