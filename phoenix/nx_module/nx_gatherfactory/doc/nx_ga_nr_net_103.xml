<?xml version="1.0" encoding="utf-8" ?>
<!--Ver 1.1.14.1 -->
<!--
配置文件制作说明:
	节点说明:
		label节点：描述配置文件修改记录等描述性内容
		table节点:表示某个表
		field节点:表示某个字段
	
	属性说明:
		name:表示表或字段的名称,如果字段是数据库的关键字或数字开头,必须进行特殊处理,否则不能存库
		des:表示表或字段的描述
		range:表示范围,用于下拉框的内容填充,格式如:range="1:ASCII格式;2:BINARY格式"
		mintomax:表示数字范围,格式如:mintomax="100:500"
		isFK:如果是外键,则填入:表名称.主键
		isEdit:如果该字段不需要界面修改,则需要isEdit="n"
		length:字段长度,每个字段必须设定
		isNull:是否为空,如果不为空,则需要设定isNull="n"
		note:某字段的扩展说明，比如note=周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭
				  table中的note属性的内容是关于对该表配置的额外说明，需要界面显示的提示（即在明显的位置放置提示Lable）给配点表人员！
	
	实例化说明：
		1.具体规约在实例话时请将用不到的表或表中某些字段删除！删除某表中的某字段时若该字段被标明非NULL（即isNull="n"）的，不可删除！
		2.如需要指明某字段的具体含义，可修改其desc和note属性，并给出默认值
-->

<GaConfig des="采集工厂配置">
	<label>
	
		<!--适用规约-->
		<ga_pro>nx_ga_nr_net_103</ga_pro>
		
		<!--文档版本版本说明,描述文档的变更情况-->
		<version des="版本修改履历">
			<update>
				<date des="日期">2014-02-12</date>
				<person des="人员">郭晨宇</person>
				<reason des="原因">首次编写该文档！</reason>
			</update>
		</version> 
		
		<!--装置端特殊配置说明,描述装置端的配置-->
		<devcfg  des="装置端规约配置">
			<cfg des="配置">
				<step des="步骤"></step>
			</cfg>
		</devcfg>
		 
		<!--常见问题说明,描述常见的现场问题-->
		<problems des="现场常见问题">
			<problem des="问题1:保护装置时间非法影响波形上送">
				<description des="描述问题1:青海330KV吉祥变RCS901B-V2.10，RCS925A-V3.00.是装置的通讯板时间异常，用TELNET登陆发现其时间均为错误时间，如“33-08-09”，“40-12-10”等几十年后的时间，所以其生成的录波时间也异常，即我们以前说的产生了非法录波，咨询南瑞厂家后说如果时间异常，会导致其不上传录波。"></description>
				<scheme des="解决方案: &#13;步骤一：用telnet登录到保护装置，设置保护装置时间为正常时间&#x0D;&#x0A;telnel ip &#13;debug &#13;nari04144 &#13;read_time &#13;set_time &quot;11-08-19 00:00:00&quot;(这里替换成现场实际时间) &#13;exit （注意：此处退出时使用exit，否则可能会影响保护通讯） &#13;步骤二：用ftp登录到保护装置，删除以前的录波文件用ftp登录到保护装置，删除以前的录波文件 &#13;ftp ip &#13;debug &#13;nari04144 &#13;bash &#13;cd c1: &#13;dir &#13;rm *  
"></scheme>
			</problem>
            <problem des="问题2">
				<description des="描述问题2:定值区号点表的配置"></description>
				<scheme des="必须要配置一个当前定值区条目，站内103条目号配置为1！"></scheme>
			</problem>
		</problems>
	</label>
	
	<tables> 
		<!--采集工厂表配置 -->
		<type name="ga_factory">
			<table name="nx_t_factory_config" desc="采集工厂配置信息表">
				<fields>
					<field name="obj_id" desc="采集口编号" mintomax="1:2000">3</field>
					<field name="aliasname"  desc="采集口名称" length="64">NR-NET</field>
					<field name="psrtype"  desc="TCP客户端" >4</field>
					<field name="protocol_obj"  desc="对应的规约" isFK="nx_t_protocol_cfg.obj_id">1</field>
					<field name="station_obj"  desc="厂站编号" isFK="nx_t_substation.obj_id">1</field>
					<field name="rev_timeout"  desc="接收超时">2000</field>
					<field name="send_timeout"  desc="发送超时">200</field>
					<field name="pmsip_a"  desc="子站UDP绑定IP地址（填写与保护通讯的本机IP地址）" length="32">***********</field>
					<field name="pmsipport_a"  desc="子站UDP绑定端口">6001</field>					<field name="pmsaddr" desc="管理机地址">8016</field>
					<field name="dataouttag"  desc="报文输出标记" range="0:不输出;1:按装置输出到多个文件;2:其他：全部输出到一个文件" note="调试时建议输出日志和报文">1</field>
					<field name="datalen"  desc="报文输出长度(大帧报文输出最大长度，单位:字节)">2560</field>
					<field name="savedatatype"  desc="保存报文类型" range="1:BINARY格式;2:ASCII格式">1</field>
					<field name="logout_tag"  desc="日志输出标记" range="0:不输出;1:按装置输出到多个文件;2:其他全部输出到一个文件">1</field>
					<field name="log_level"  desc="日志级别" range="1:仅记录错误日志;2:记录错误及跟踪类所有日志;3:记录所有相关日志">3</field>
					<field name="rcd_days"  desc="日志记录天数(日志及报文记录的总天数.0:表示不记录日志及报文)">3</field>
					<field name="print2screen"  desc="日志、报文是否输出到屏幕" range="1:输出;0:不输出">1</field>
				</fields>
			</table>
			
			<!--采集规约动态库配置 -->
			<table name="nx_t_protocol_cfg" desc="通讯规约配置表">
				<field name="obj_id" desc="规约编号" mintomax="1:9999">1</field>
				<field name="aliasname" desc="规约名称"></field>
				<field name="prodllname"  desc="动态库名称(不带文件后缀)">nx_ga_nr_net_103</field>
				<!--这里必须为1-->
				<field name="workrole"  desc="规约角色(1:IED接入  2：信息远传)" isEdit="n">1</field>
				<field name="manufacturer"  desc="厂家（直接填写厂家中文名称，禁止空格">南瑞继保</field>
			</table>
			
		  <table name="nx_t_factory_cyc_time_config" desc="采集工厂自动轮询周期信息表">
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">1</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询动作周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">5</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">2</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询告警周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
			
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">3</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询开关量周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
								
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">4</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询定值周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">5</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询模拟量周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">6</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询软压板周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">7</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询当前定值区号周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">8</field>
					<field name="aliasname" desc="周期名称" isEdit="n">时钟同步周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">9</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询新文件周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">300</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>				
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">10</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询通讯状态周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">61</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>	
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">11</field>
					<field name="aliasname" desc="周期名称" isEdit="n">重新登陆周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">67</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>
				
				<fields>
					<field name="factory_obj" desc="工厂编号" isFK="nx_t_protocol_cfg.obj_id">3</field>
					<field name="cycle_code" desc="周期编号" isEdit="n">12</field>
					<field name="aliasname" desc="周期名称" isEdit="n">查询录波列表周期</field>
					<field name="curvalue" desc="周期数值" length="6" note="周期数值,单位为秒，当为0时关闭周期，未配置的时候，默认为关闭">0</field>
					<field name="param1" desc="保留字段1(每个规约实例化时如需填写则去掉isEdit,下同)" isEdit="n" length="64"></field>
					<field name="param2" desc="保留字段2" isEdit="n" length="64"></field>
				</fields>	
				
			</table>
		</type>
		
		<!-- IED配置-->
		<type name="IED_config">
			<table name="nx_t_ied" desc="智能设备基本信息表">
				<field name="obj_id" desc="IED编号" note="从1开始编写">1</field>
				<field name="aliasname"  desc="设备名称" length="64" isNull="n"></field>
				<field name="abbrename"  desc="IED名称缩写" length="64"></field>
				<field name="station_obj"  desc="所属变电站" isFK="nx_t_substation.obj_id">1</field>
				<field name="primequ_obj"  desc="关联一次设备" isFK="nx_t_primequipment.obj_id">1</field>
				<field name="opramode"  desc="当前运行状态" range="0:检修;1:停运;2:投运;3:未接入;4:调试(对码表);5:其它，未知">2</field>
				<field name="model"  desc="型号" isFK="nx_t_ied_type.ied_type" length="64">WXH-803A</field>
				<field name="psrtype"  desc="类型" range="-1:其它编号,未知;0:子站;1:线路保护;2:变压器保护;3:母差保护;4:母联保护;5:发变组保护;6:开关保护;7:电容器保护;8:电抗器保护;10:故障录波器;11:网络报文记录仪;12:测控装置;13:电能表;14:安稳装置;15:低周减载装置;16:低频解列装置;17:GPS装置;18:合并单元;19:智能终端;20:交换机;21:路由器">1</field>
				<field name="addr"  desc="站内设备地址" note="站内地址，用于数据采集，不大于255" mintomax="1:255">1</field>
				<field name="ipaddr_a"  desc="保护A网IP地址" length="32">************</field>
				<field name="ipport_a"  desc="保护A网端口" length="4">6000</field>
				<field name="ipaddr_b"  desc="保护B网IP地址" length="32"></field>
				<field name="ipport_b"  desc="保护B网端口" length="4"></field>
				<field name="factory_obj"  desc="采集口编号" isFK="nx_t_factory_config.obj_id">1</field>
				<field name="iedversion"  desc="装置版本" length="32"></field>
				<field name="chkcode"  desc="装置校验码" length="32"></field>
				<field name="programtm"  desc="程序生成时间" note="格式： 2013-12-12 12:12:12"></field>
				<field name="workrole"  desc="工作角色" range="1:主保护;2:后备保护">1</field>
		    </table>
			
		   <!-- CPU配置-->
			<table name="nx_t_ied_ld" desc="IED逻辑设备(CPU)表"> 
				<fields>
					<field name="ld_code" desc="ld的编号" note="要求同一装置编号不相同,如果装置不是多CPU，默认建1个LD" isNull="n">1</field>
					<field name="ied_obj" desc="所属IED" isPK="nx_t_ied.obj_id"></field>
					<field name="aliasname"  desc="逻辑设备名称" length="64">CPU1</field>
					<field name="ldversion"  desc="版本" length="64"></field>
					<field name="chkcode"  desc="校验码"  length="32"></field>
					<field name="ldfun"  desc="功能号" note="用于采集模块录波文件召唤，和CPU完成的保护功能相关">178</field>
				</fields>
			</table>
			
			<!-- 设备扩展配置-->
			<table name="nx_t_factory_ied_config" desc="采集工厂对二次设备的访问处理配置表">
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id"></field>
				<field name="file_type"  desc="录波文件类型" range="0:以ASCII存入;1:以BINAR">0</field>
				<field name="precycles"  desc="故障前预录的周波数" >2</field>
				<field name="prefreq"  desc="故障前预录的频率" >50</field>
				<field name="cmdflag"  desc="强制召唤标志" range="0:不强制召唤;1:强制召唤">0</field>
				<field name="calltimes"  desc="重发报文次数" note="命名失败后，重发报文次数">3</field>
				<field name="calldata1times"  desc="重发一级报文次数" note="提示没有一级数据后，重发召唤一级数据次数">1</field>
				<field name="param1"  desc="召唤录波的方式" note="召唤录波的方式，配置成1为扰动数据方式(通过总召唤请求列表)，配置成2为扰动数据方式(通过下发ASDU24请求列表)，配置为其它为召唤文件方式，使用此规约接入南瑞科技保护需要使用文件方式召唤" range="0:文件方式;1:扰动数据（总召唤）方式;2:扰动数据（下发ASDU24报文）">2</field>
				<field name="param2"  desc="订单超时时间" note="取值范围:大于等于30且小于等于300，不配取缺省值30，单位:s" range="">30</field>
		  </table> 
		</type>
		<!-- IED配置结束-->
		
		<!-- 以下是点表配置 -->
		<type name="LD_config"> 
			<!-- 通用分类组号配置－对于103规约装置有用-->
		    <table name="nx_t_ied_group_config" desc="IED通用分类服务数据表">
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="psrtype" desc="组类型描述" range="1:动作组;2:告警组;3:开关量组;4:定值组;5:模拟量组;6:软压版组;7:当前定值区号组;8:基本信息组;9:特征值组;14:录波开关量通道组;15:录波模拟量通道组" isNull="n"></field>
				<field name="call_gin" desc="通用分类服务召唤组号"  isNull="n"></field>
				<field name="send_gin" desc="通用分类服务上送的组号"   note="当上送的组号和召唤时下发的组号不同时，可以使用该字，缺省是使用召唤组号"></field>
				<field name="change_gin" desc="通用分类服务控制的组号"  note="当控制的组号和召唤时下发的组号不同时，可以使用该字段，缺省是使用召唤组号"></field>
			</table> 
			
			<!--定值配置表-->
			<table name="nx_t_ied_sg_cfg" desc="IED逻辑设备下的定值配置表" note="">
				<field name="sg_code" desc="定值编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="定值名称" length="128">none</field>
				<field name="abbrename" desc="定值代码" length="64" note="定值名称缩写">none</field>
				<field name="psrdatatype" desc="数据类型" range="0:浮点;1:整型;2:控制字(十六进制);3:字符串;4:控制字(二进制)">0</field>
				<field name="ctrlwordlist" desc="控制字使用及描述" note="从高到底顺序对控制字逐位进行解释，中间用“$”分隔离，如某一位未使用，则描述为“未定义" length="512">0</field>
				<field name="sgunit" desc="单位" note="如A、V、W、kA、kV、kW、s、ms、Ω等。LFP900系列保护的阻抗单位必须为Ω" length="16">V</field>
				<field name="sgpresion" desc="精度" note="格式为：a.b，a表示整数部分最大位，b表示小数点后最大位" length="8">3.2</field>
				<field name="downlimit" desc="下限">0</field>
				<field name="uplimit" desc="上限">65535</field>
				<field name="fratio" desc="折算率" note="实际值=值*fratio，也可用于界面显示，如值为毫秒，界面要求显示为秒，在此处设置fratio=0.001即可">1.0</field>
				<field name="iedgroup103" desc="ied103组号">3</field>
				<field name="ieditem103" desc="ied103条目号">2</field>
				<field name="psrfuntpye" desc="功能类型" range="0: 普通定值;1:当前定值区号;2:编辑定值区;3:检修标志;4:重合闸类;5:纵联差动保护类;6:距离保护类;7:零序类;8:功能投退类">0</field>
			</table> 
			
			<!-- 定值区号配置表-->
			<table name="nx_t_ied_sgzone_cfg" desc="">
				<field name="sg_code" desc="定值区号编号" isNull="n">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="psrtype" desc="值类型" isPK="nx_t_ied_ld.ld_code" isNull="n" range="1:当前区;2:编辑区">1</field>
				<field name="aliasname" desc="定值区名称" isNull="n" length="64"></field>
				<field name="downlimit" desc="下限">0</field>
				<field name="uplimit" desc="上限">32</field>
			</table> 
			
			<!-- 模拟量配置表 -->
			<table name="nx_t_ied_analog_cfg" desc="IED逻辑设备下的模拟量配置表">
				<field name="ana_code" desc="模拟量编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="模拟量中文名称" length="128">none</field>
				<field name="abbrename" desc="模拟量代码" length="64" note="定值名称缩写">none</field>
				<field name="psrdatatype" desc="数据类型" range="0:浮点;1:整型;2:控制字(十六进制);3:字符串;4:控制字(二进制)">0</field>
				<field name="anaunit" desc="单位" note="如A、V、W、kA、kV、kW、s、ms、Ω等。LFP900系列保护的阻抗单位必须为Ω" length="16">V</field>
				<field name="anapresion" desc="精度" note="格式为：a.b，a表示整数部分最大位，b表示小数点后最大位" length="8">2.2</field>
				<field name="downlimit" desc="下限">-999</field>
				<field name="uplimit" desc="上限">999</field>
				<field name="fratio" desc="折算率" note="实际值=值*fratio，也可用于界面显示，如值为毫秒，界面要求显示为秒，在此处设置fratio=0.001即可">1.0</field>
				<field name="iedgroup103" desc="ied103组号"></field>
				<field name="ieditem103" desc="ied103条目号"></field>
			</table>
					
			<!-- 软压板配置表-->
			<table name="nx_t_ied_softstrap_cfg" desc="IED逻辑设备下的软压板配置表" note="">
				<field name="strap_code" desc="软压板编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="压板名称" length="128">none</field>
				<field name="abbrename" desc="压板代码" length="64" note="定值名称缩写">none</field>
				<field name="iedgroup103" desc="ied103组号"></field>
				<field name="ieditem103" desc="ied103条目号"></field>
				<field name="basevalue" desc="基准值">1</field>
				<field name="basevaluetm" desc="基准值更新时间" length="32" note="格式： 2013-12-12 12:12:12"></field>
				<field name="basevaluesrc" desc="基准值来源" range="1:IED召唤;2:整定系统;3:人工置数">1</field>
				<field name="psrfuntype" desc="压板功能类型" range="0:未知压板;2:检修标志3:重合闸类;4:纵联差动保护类5:距离保护类;6:零序类">0</field>
			</table> 
	
			<!-- 硬压板配置表-->
			<table name="nx_t_ied_hardstrap_cfg" desc="IED逻辑设备下的硬压板配置表" note="">
				<field name="strap_code" desc="硬压板编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="压板名称" length="128">none</field>
				<field name="abbrename" desc="压板代码" length="64" note="定值名称缩写">none</field>
				<field name="iedgroup103" desc="ied103组号"></field>
				<field name="ieditem103" desc="ied103条目号"></field>
				<field name="basevalue" desc="基准值">1</field>
				<field name="basevaluetm" desc="基准值更新时间" length="32" note="格式： 2013-12-12 12:12:12"></field>
				<field name="basevaluesrc" desc="基准值来源" range="1:IED召唤;2:整定系统;3:人工置数">1</field>
				<field name="psrfuntype" desc="压板功能类型" range="0:未知压板;2:检修标志3:重合闸类;4:纵联差动保护类5:距离保护类;6:零序类">0</field>
			</table> 
			
			<!-- 动作事件配置表-->
			<table name="nx_t_ied_event_cfg" desc="IED逻辑设备下的动作事件和告警事件配置表">
				<field name="event_code" desc="事件编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="事件中文描述" length="128">none</field>
				<field name="abbrename" desc="事件代码" length="64" note="事件名称缩写">none</field>
				<field name="psrgradetype" desc="事件级别" range="1:一级事件;2:二级事件;3:三级事件">1</field>
				<field name="iedgroup103" desc="功能类型FUN"  length="32"></field>
				<field name="ieditem103" desc="信息序号INF" length="32"></field>
				<field name="psrfuntype" desc="事件功能类型" range="0:未知类型;3:重合闸动作;4:纵联保护动作;5:距离保护动作6:三段距离动作;7:零序距离动作;100:装置异常;101:回路异常;102:通道异常;103:其它异常;104:装置检修">0</field>
			</table> 
			
			<!-- 告警事件配置表-->
			<table name="nx_t_ied_alarm_cfg" desc="IED逻辑设备下的告警配置表">
				<field name="alarm_code" desc="告警编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="告警中文描述" length="128">none</field>
				<field name="abbrename" desc="告警代码" length="64" note="告警名称缩写">none</field>
				<field name="psrgradetype" desc="告警级别" range="1:一级告警;2:二级告警;3:三级告警">1</field>
				<field name="iedgroup103" desc="功能类型FUN"  length="32"></field>
				<field name="ieditem103" desc="信息序号INF" length="32"></field>
				<field name="psrfuntype" desc="告警功能类型" range="0:未知类型;3:重合闸动作;4:纵联保护动作;5:距离保护动作6:三段距离动作;7:零序距离动作;100:装置异常;101:回路异常;102:通道异常;103:其它异常;104:装置检修">0</field>
			</table> 
			
			<!-- 故障量配置表-->
			<table name="nx_t_ied_faulttag_cfg" desc="IED逻辑设备下的故障参数表">
				<field name="alarm_code" desc="特征量编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="特征量中文描述" length="128">none</field>
				<field name="abbrename" desc="特征量代码" length="64" note="特征量名称缩写">none</field>
				<field name="psrdatatype" desc="数据类型" range="0:浮点;1:整型;2:控制字(十六进制);3:字符串;4:控制字(二进制)">0</field>
				<field name="anaunit" desc="单位" note="如A、V、W、kA、kV、kW、s、ms、Ω等。LFP900系列保护的阻抗单位必须为Ω" length="16">V</field>
				<field name="anapresion" desc="精度" note="格式为：a.b，a表示整数部分最大位，b表示小数点后最大位" length="8">2.2</field>
				<field name="iedgroup103" desc="动作信息序号" note="对应动作表中的事件点INF号" length="32"></field>
				<field name="ieditem103" desc="故障量序号"  note="从1开始累加" length="32">1</field>
				<field name="psrfuntype" desc="特征量功能类型" range="0:未配置;1:电网故障序号;2:故障序号;3:故障测距;;4:故障相别;5:跳闸相别;10:故障前电流11:故障前有功;12:故障前无功;13:故障时最大故障电流;14:故障时最大零序电流;15:故障时电阻;16:故障时电抗">0</field>
			</table> 
			
			<!-- 录波模拟量配置表-->
			<table name="nx_t_ied_osc_ai_cfg" desc="IED逻辑设备下的模拟量配置表">
				<field name="ai_code" desc="通道编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="模拟量通道中文名称" length="128">未配置</field>
				<field name="abbrename" desc="代码" length="64" note="名称缩写"></field>
				<field name="psrdatatype" desc="通道类型" range="1:电流通道;2:电压通道;3:功率;4:高频">1</field>
				<field name="a" desc="通道比例因子" note="比率因子">1.0</field>
				<field name="b" desc="通道偏移量" note="比率因子">0.0</field>
				<field name="phase" desc="通道相别" note="A,B,C,N，O  N表示零序 O：表示其它" length="8">A</field>
				<field name="anaunit" desc="单位" note="如A、V、W、kA、kV、kW、s、ms、Ω等。LFP900系列保护的阻抗单位必须为Ω" length="16">V</field>
				<field name="downlimit" desc="下限">-32767</field>
				<field name="uplimit" desc="上限">32767</field>
				<field name="ctptratio" desc="CT或PT变比">1.0</field>
				<field name="primtag" desc="一次值标记" range="0:二次值;1:一次值">0</field>
				<field name="equ_obj" desc="关联一次设备" note="实现录波通道和一次设备关联" isPK="nx_t_primequipment.obj_id">0</field>
				<field name="iedgroup103" desc="ACC通道序号"  length="32"></field>
			</table> 
			
			<!-- 录波开关量配置表-->
			<table name="nx_t_ied_osc_di_cfg" desc="IED录波开关量通道配置表">
				<field name="di_code" desc="通道编号" isNull="n" note="要求同一IED同一LD下从1开始编号，编号不重复">1</field>
				<field name="ied_obj" desc="IED编号" isPK="nx_t_ied.obj_id" isNull="n">1</field>
				<field name="ld_code" desc="CPU号" isPK="nx_t_ied_ld.ld_code" isNull="n">1</field>
				<field name="aliasname" desc="模拟量通道中文名称" length="128">未配置</field>
				<field name="abbrename" desc="代码" length="64" note="名称缩写"></field>
				<field name="phase" desc="通道相别" note="A,B,C,N，O  N表示零序 O：表示其它" length="8">A</field>
				<field name="normalopen" desc="默认状态" note="若常闭则置为1   常开为0" range="1:常闭;0:常开">1</field>
				<field name="equ_obj" desc="关联一次设备" note="实现录波通道和一次设备关联" isPK="nx_t_primequipment.obj_id">0</field>
				<field name="iedgroup103" desc="录波FUN号" note="录波开关量功能号"></field>
				<field name="ieditem103" desc="录波INF号" note="录波开关量信息序号"></field>
				<field name="psrfuntype" desc="事件功能类型" range="0:断路器变位;1:重合闸动作;2:保护跳闸;3:保护告警;4:IED检修状态">3</field>
			</table> 
		<!-- 点表配置结束！ -->
	</type>
  </tables>
 </GaConfig> 