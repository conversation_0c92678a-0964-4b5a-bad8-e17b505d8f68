<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL" xsi:schemaLocation="http://www.iec.ch/61850/2003/SCL SCL.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:IEC_60870_5_104="http://www.iec.ch/61850-80-1/2007/SCL" xmlns:sip4="http://www.siemens.com/energy/2009/09/siprotec4/Sip4" xmlns:siedig="http://www.siemens.com/energy/2011/11/Siedig">
	<Private type="Siemens-SclLib-V5-Version">V09.20.02.000</Private>
	<Private type="Siemens-SclLib-V5-EditTime">27.03.2024 15:58:54</Private>
	<Private type="Siemens-Configurator-Version">7.20.50</Private>
	<Header id="Siemens_7SJ62" version="1" revision="8" toolID="IEC 61850 System Configurator, Version: 7.20.50" nameStructure="IEDName">
		<Text>Cid-File for device: Siemens_7SJ62</Text>
		<History>
			<Hitem version="1" revision="8" when="Wednesday, March 27, 2024 3:58:54 PM" who="Licenced User:  Ron Machine: DESKTOP-FCAPD7M User: Ron" what="Device Siemens_7SJ62 is exported as Edition1 CID file." why="IEC 61850 System Configurator User Action" />
		</History>
	</Header>
	<Communication>
		<SubNetwork name="S1" type="8-MMS">
			<Private type="Siemens-Start-Address">**********</Private>
			<Private type="Siemens-Default-Subnet-Mask">***********</Private>
			<ConnectedAP iedName="Siemens_7SJ62" apName="P1">
				<Private type="Siemens-Redundant-SubnetId">1</Private>
				<Address>
					<P type="OSI-AP-Title">1,3,9999,23</P>
					<P type="OSI-AE-Qualifier">23</P>
					<P type="OSI-PSEL">00000001</P>
					<P type="OSI-SSEL">0001</P>
					<P type="OSI-TSEL">0001</P>
					<P type="IP" xsi:type="tP_IP">**********</P>
					<P type="IP-SUBNET" xsi:type="tP_IP-SUBNET">***********</P>
					<P type="IP-GATEWAY" xsi:type="tP_IP-GATEWAY">0.0.0.0</P>
				</Address>
			</ConnectedAP>
		</SubNetwork>
	</Communication>
	<IED desc="7SJ622 V4.9 RPIMS" name="Siemens_7SJ62" type="Siprotec-7SJ6xx" manufacturer="SIEMENS" configVersion="1.0">
		<Private type="Siemens-Inkrement">2</Private>
		<Private type="Siemens-BRCB-StoragePercentageMemoryForBuffer">PROT\LLN0\brcbA|50</Private>
		<Private type="Siemens-BRCB-StoragePercentageMemoryForBuffer">PROT\LLN0\brcbB|50</Private>
		<Private type="Siemens-NetworkFrequency">50 Hz</Private>
		<Private type="Siemens-s7ManagerName">7SJ622 V4.9 RPIMS</Private>
		<Private type="Siemens-IED-Id">2_IED_0</Private>
		<Private type="Siemens-ICD-Language">en-GB</Private>
		<Private type="Siemens-ModifiedDate">03/27/2024 07:55:05</Private>
		<Private type="Siemens-ModificationCounter">12</Private>
		<Private type="Siemens-CID-MainIED" />
		<Services>
			<!--DynReportControl max="10"/-->
			<!-- present in FDIS, removed in IS, commented by SIEMENS -->
			<DynAssociation />
			<SettingGroups />
			<GetDirectory />
			<GetDataObjectDefinition />
			<DataObjectDirectory />
			<GetDataSetValue />
			<DataSetDirectory />
			<ConfDataSet max="34" maxAttributes="100" modify="true" />
			<DynDataSet max="15" maxAttributes="60" />
			<ReadWrite />
			<ConfReportControl max="18" />
			<GetCBValues />
			<ReportSettings cbName="Fix" datSet="Dyn" rptID="Dyn" optFields="Dyn" bufTime="Dyn" trgOps="Dyn" intgPd="Dyn" />
			<GSESettings cbName="Conf" datSet="Conf" appID="Conf" />
			<GOOSE max="16" />
			<FileHandling />
			<ConfLNs fixPrefix="true" fixLnInst="true" />
		</Services>
		<AccessPoint name="P1">
			<Server>
				<Authentication none="true" />
				<LDevice desc="Protection" inst="PROT">
					<LN0 lnClass="LLN0" inst="" lnType="Siemens_7SJ62/PROT/LLN0" desc="General">
						<Private type="Siemens-ControlBlockStorage">urcbE|&lt;ReportControl rptID="Siemens_7SJ62PROT/LLN0$RP$urcbE01" confRev="1" buffered="false" bufTime="100" name="urcbE"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbF|&lt;ReportControl rptID="Siemens_7SJ62PROT/LLN0$RP$urcbF01" confRev="1" buffered="false" bufTime="100" name="urcbF"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbG|&lt;ReportControl rptID="Siemens_7SJ62PROT/LLN0$RP$urcbG01" confRev="0" buffered="false" bufTime="100" name="urcbG"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbH|&lt;ReportControl rptID="Siemens_7SJ62PROT/LLN0$RP$urcbH01" confRev="0" buffered="false" bufTime="100" name="urcbH"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbI|&lt;ReportControl rptID="Siemens_7SJ62PROT/LLN0$RP$urcbI01" confRev="0" buffered="false" bufTime="100" name="urcbI"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbJ|&lt;ReportControl rptID="Siemens_7SJ62PROT/LLN0$RP$urcbJ01" confRev="0" buffered="false" bufTime="100" name="urcbJ"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DataSet name="dsTripInfo">
							<FCDA ldInst="PROT" prefix="" lnClass="PTOC" lnInst="6" doName="Str" fc="ST" />
							<FCDA ldInst="PROT" prefix="" lnClass="PTOC" lnInst="6" doName="Op" fc="ST" />
							<FCDA ldInst="PROT" prefix="" lnClass="PTOC" lnInst="7" doName="Str" fc="ST" />
							<FCDA ldInst="PROT" prefix="" lnClass="PTOC" lnInst="7" doName="Op" fc="ST" />
							<FCDA ldInst="PROT" prefix="" lnClass="PTOC" lnInst="8" doName="Str" fc="ST" />
							<FCDA ldInst="PROT" prefix="" lnClass="PTOC" lnInst="8" doName="Op" fc="ST" />
							<FCDA ldInst="PROT" prefix="" lnClass="PTOC" lnInst="9" doName="Str" fc="ST" />
							<FCDA ldInst="PROT" prefix="" lnClass="PTOC" lnInst="9" doName="Op" fc="ST" />
						</DataSet>
						<DataSet name="dsDin">
							<FCDA ldInst="DR" prefix="SC_" lnClass="RDRE" lnInst="1" doName="RcdMade" fc="ST" />
							<FCDA ldInst="CTRL" prefix="Q0" lnClass="XCBR" lnInst="1" doName="Pos" fc="ST" />
							<FCDA ldInst="EXT" prefix="pd" lnClass="GGIO" lnInst="27" doName="SPCSO57" fc="ST" />
							<FCDA ldInst="EXT" prefix="pd" lnClass="GGIO" lnInst="27" doName="SPCSO60" fc="ST" />
							<FCDA ldInst="EXT" prefix="pd" lnClass="GGIO" lnInst="1" doName="SPCSO5" fc="ST" />
							<FCDA ldInst="EXT" prefix="pd" lnClass="GGIO" lnInst="513" doName="SPCSO33" fc="ST" />
						</DataSet>
						<DataSet name="dsAlarm">
							<FCDA ldInst="EXT" prefix="pd" lnClass="GGIO" lnInst="3" doName="SPCSO34" fc="ST" />
							<FCDA ldInst="EXT" prefix="pd" lnClass="GGIO" lnInst="3" doName="SPCSO35" fc="ST" />
							<FCDA ldInst="EXT" prefix="pd" lnClass="GGIO" lnInst="3" doName="SPCSO39" fc="ST" />
						</DataSet>
						<ReportControl datSet="dsAlarm" rptID="Siemens_7SJ62PROT/LLN0$RP$urcbA01" confRev="1" buffered="false" bufTime="100" name="urcbA">
							<TrgOps dchg="true" qchg="true" dupd="true" period="true" />
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" />
							<RptEnabled max="1" />
						</ReportControl>
						<ReportControl datSet="dsAlarm" rptID="Siemens_7SJ62PROT/LLN0$RP$urcbB01" confRev="1" buffered="false" bufTime="100" name="urcbB">
							<TrgOps dchg="true" qchg="true" dupd="true" period="true" />
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" />
							<RptEnabled max="1" />
						</ReportControl>
						<ReportControl datSet="dsDin" rptID="Siemens_7SJ62PROT/LLN0$RP$urcbC01" confRev="40001" buffered="false" bufTime="100" name="urcbC">
							<TrgOps dchg="true" qchg="true" dupd="true" period="true" />
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" />
							<RptEnabled max="1" />
						</ReportControl>
						<ReportControl datSet="dsDin" rptID="Siemens_7SJ62PROT/LLN0$RP$urcbD01" confRev="40001" buffered="false" bufTime="100" name="urcbD">
							<TrgOps dchg="true" qchg="true" dupd="true" period="true" />
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" />
							<RptEnabled max="1" />
						</ReportControl>
						<ReportControl datSet="dsTripInfo" rptID="Siemens_7SJ62PROT/LLN0$BR$brcbA01" confRev="1" buffered="true" bufTime="100" name="brcbA">
							<TrgOps dchg="true" qchg="true" dupd="true" period="true" />
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" />
							<RptEnabled max="1" />
						</ReportControl>
						<ReportControl datSet="dsTripInfo" rptID="Siemens_7SJ62PROT/LLN0$BR$brcbB01" confRev="1" buffered="true" bufTime="100" name="brcbB">
							<TrgOps dchg="true" qchg="true" dupd="true" period="true" />
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" />
							<RptEnabled max="1" />
						</ReportControl>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>direct-with-enhanced-security</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<!-- Die DAI vendor, swRev, d, configRev und ldNs beziehen sich auf das EN100-Modul. Bis auf ldNs haben sie keinen InitValue! Sie werden mit Dummy vorbelegt. Gefuellt werden sie durch die EN100-Software zur Laufzeit. -->
							<DAI name="configRev">
								<Val>210404153620012</Val>
							</DAI>
							<DAI name="ldNs">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
						</DOI>
						<DOI name="OpTmh" desc="Operating hours" />
						<SettingControl numOfSGs="1" />
					</LN0>
					<LN prefix="" lnClass="LPHD" inst="1" lnType="Siemens_7SJ62/PROT/LPHD1" desc="Device">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/LPHD1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/LPHD1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="PhyNam" desc="Info" />
						<DOI name="PhyHealth" desc="State" />
						<DOI name="Proxy" desc="Proxy" />
					</LN>
					<LN prefix="" lnClass="PTRC" inst="1" lnType="Siemens_7SJ62/PROT/PTRC1" desc="Total">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTRC1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTRC1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Tr" desc="OFF" />
						<DOI name="Str" desc="Pickup" />
					</LN>
					<LN prefix="" lnClass="XCBR" inst="1" lnType="Siemens_7SJ62/PROT/XCBR1" desc="CB">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/XCBR1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/XCBR1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="local" />
						<DOI name="OpCnt" desc="Operat. counter" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkOpn" desc="Open block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkCls" desc="Close block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="SumSwARsA" desc="Sum IL1">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
							<DAI name="d">
								<Val>SumSwARsA stands for Accumulation of interrupted current L1</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SJ6 IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="SumSwARsB" desc="Sum IL2">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
							<DAI name="d">
								<Val>SumSwARsB stands for Accumulation of interrupted current L2</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SJ6 IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="SumSwARsC" desc="Sum IL3">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
							<DAI name="d">
								<Val>SumSwARsC stands for Accumulation of interrupted current L3</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SJ6 IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="CBOpCap" desc="Switch capablty">
							<DAI name="stVal">
								<Val>None</Val>
							</DAI>
						</DOI>
						<DOI name="SwAphs" desc="I">
							<SDI name="phsA">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="phsB">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="phsC">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
						</DOI>
					</LN>
					<LN prefix="" lnClass="PTOC" inst="6" lnType="Siemens_7SJ62/PROT/PTOC6" desc="DMT I&gt;">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC6$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC6$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Str" desc="Pickup" />
						<DOI name="Op" desc="OFF" />
					</LN>
					<LN prefix="" lnClass="PTOC" inst="7" lnType="Siemens_7SJ62/PROT/PTOC6" desc="DMT I&gt;&gt;">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC7$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC7$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Str" desc="Pickup" />
						<DOI name="Op" desc="OFF" />
					</LN>
					<LN prefix="" lnClass="PTOC" inst="8" lnType="Siemens_7SJ62/PROT/PTOC6" desc="DMT IE&gt;">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC8$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC8$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Str" desc="Pickup" />
						<DOI name="Op" desc="OFF" />
					</LN>
					<LN prefix="" lnClass="PTOC" inst="9" lnType="Siemens_7SJ62/PROT/PTOC6" desc="DMT IE&gt;&gt;">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC9$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC9$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Str" desc="Pickup" />
						<DOI name="Op" desc="OFF" />
					</LN>
					<LN prefix="" lnClass="PTOC" inst="18" lnType="Siemens_7SJ62/PROT/PTOC6" desc="DMT I&gt;&gt;&gt;">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC18$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTOC18$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Str" desc="Pickup" />
						<DOI name="Op" desc="OFF" />
					</LN>
					<LN prefix="" lnClass="PTRC" inst="2" lnType="Siemens_7SJ62/PROT/PTRC2" desc="DMT general">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTRC2$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62PROT/PTRC2$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Op" desc="OFF" />
						<DOI name="Str" desc="Pickup" />
					</LN>
				</LDevice>
				<LDevice desc="Measurement" inst="MEAS">
					<LN0 lnClass="LLN0" inst="" lnType="Siemens_7SJ62/MEAS/LLN0" desc="General">
						<Private type="Siemens-ControlBlockStorage">urcbC|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbC01" confRev="0" buffered="false" bufTime="100" name="urcbC"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbD|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbD01" confRev="0" buffered="false" bufTime="100" name="urcbD"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbE|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbE01" confRev="0" buffered="false" bufTime="100" name="urcbE"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbF|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbF01" confRev="0" buffered="false" bufTime="100" name="urcbF"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbG|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbG01" confRev="0" buffered="false" bufTime="100" name="urcbG"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbH|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbH01" confRev="0" buffered="false" bufTime="100" name="urcbH"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbI|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbI01" confRev="0" buffered="false" bufTime="100" name="urcbI"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbJ|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbJ01" confRev="0" buffered="false" bufTime="100" name="urcbJ"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">brcbA|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$BR$brcbA01" confRev="0" buffered="true" bufTime="100" name="brcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">brcbB|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LLN0$BR$brcbB01" confRev="0" buffered="true" bufTime="100" name="brcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DataSet name="dsAin">
							<FCDA ldInst="MEAS" prefix="" lnClass="MMXU" lnInst="1" doName="PhV.phsA" fc="MX" />
							<FCDA ldInst="MEAS" prefix="" lnClass="MMXU" lnInst="1" doName="PhV.phsB" fc="MX" />
							<FCDA ldInst="MEAS" prefix="" lnClass="MMXU" lnInst="1" doName="PhV.phsC" fc="MX" />
							<FCDA ldInst="MEAS" prefix="" lnClass="MMXU" lnInst="1" doName="PhV.neut" fc="MX" />
							<FCDA ldInst="MEAS" prefix="" lnClass="MMXU" lnInst="1" doName="A.phsA" fc="MX" />
							<FCDA ldInst="MEAS" prefix="" lnClass="MMXU" lnInst="1" doName="A.phsB" fc="MX" />
							<FCDA ldInst="MEAS" prefix="" lnClass="MMXU" lnInst="1" doName="A.phsC" fc="MX" />
							<FCDA ldInst="MEAS" prefix="" lnClass="MMXU" lnInst="1" doName="A.neut" fc="MX" />
						</DataSet>
						<ReportControl datSet="dsAin" rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbA01" confRev="1" buffered="false" bufTime="100" name="urcbA">
							<TrgOps dchg="true" qchg="true" dupd="true" period="true" />
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" />
							<RptEnabled max="1" />
						</ReportControl>
						<ReportControl datSet="dsAin" rptID="Siemens_7SJ62MEAS/LLN0$RP$urcbB01" confRev="1" buffered="false" bufTime="100" name="urcbB">
							<TrgOps dchg="true" qchg="true" dupd="true" period="true" />
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" />
							<RptEnabled max="1" />
						</ReportControl>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
							<DAI name="ldNs">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
						</DOI>
					</LN0>
					<LN prefix="" lnClass="MMXU" inst="1" lnType="Siemens_7SJ62/MEAS/MMXU1" desc="Op meas value">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62MEAS/MMXU1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62MEAS/MMXU1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="TotW" desc="P">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
							<DAI name="db">
								<Val>10000</Val>
							</DAI>
						</DOI>
						<DOI name="TotVAr" desc="Q">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
							<DAI name="db">
								<Val>10000</Val>
							</DAI>
						</DOI>
						<DOI name="TotVA" desc="S">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
							<DAI name="db">
								<Val>10000</Val>
							</DAI>
						</DOI>
						<DOI name="TotPF" desc="cos">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
							<DAI name="db">
								<Val>10000</Val>
							</DAI>
						</DOI>
						<DOI name="Hz" desc="f">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
							<DAI name="db">
								<Val>100</Val>
							</DAI>
						</DOI>
						<DOI name="PPV" desc="U">
							<SDI name="phsAB">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>2000</Val>
								</DAI>
							</SDI>
							<SDI name="phsBC">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>2000</Val>
								</DAI>
							</SDI>
							<SDI name="phsCA">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>2000</Val>
								</DAI>
							</SDI>
						</DOI>
						<DOI name="PhV" desc="UE">
							<SDI name="phsA">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>2000</Val>
								</DAI>
							</SDI>
							<SDI name="phsB">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>2000</Val>
								</DAI>
							</SDI>
							<SDI name="phsC">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>2000</Val>
								</DAI>
							</SDI>
							<SDI name="neut">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>2000</Val>
								</DAI>
							</SDI>
						</DOI>
						<DOI name="A" desc="I">
							<SDI name="phsA">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="phsB">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="phsC">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="neut">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
						</DOI>
					</LN>
					<LN prefix="" lnClass="MMTR" inst="1" lnType="Siemens_7SJ62/MEAS/MMTR1" desc="Energy counter">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62MEAS/MMTR1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62MEAS/MMTR1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="SupWh" desc="Wp output">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
						</DOI>
						<DOI name="SupVArh" desc="Wq output">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
						</DOI>
						<DOI name="DmdWh" desc="Wp Input">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
						</DOI>
						<DOI name="DmdVArh" desc="Wq Input">
							<SDI name="units">
								<DAI name="SIUnit" />
							</SDI>
						</DOI>
					</LN>
					<LN prefix="" lnClass="MSQI" inst="1" lnType="Siemens_7SJ62/MEAS/MSQI1" desc="Sym comp value">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62MEAS/MSQI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62MEAS/MSQI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="SeqA" desc="Current">
							<SDI name="c1">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="c2">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="c3">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<DAI name="seqT">
								<Val>pos-neg-zero</Val>
							</DAI>
						</DOI>
						<DOI name="SeqV" desc="Voltage">
							<SDI name="c1">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="c2">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<SDI name="c3">
								<SDI name="units">
									<DAI name="SIUnit" />
								</SDI>
								<DAI name="db">
									<Val>10000</Val>
								</DAI>
							</SDI>
							<DAI name="seqT">
								<Val>pos-neg-zero</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="" lnClass="LPHD" inst="1" lnType="Siemens_7SJ62/PROT/LPHD1" desc="Device">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LPHD1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62MEAS/LPHD1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="PhyNam" desc="Info" />
						<DOI name="PhyHealth" desc="State" />
						<DOI name="Proxy" desc="Proxy" />
					</LN>
				</LDevice>
				<LDevice desc="Disturb Rec" inst="DR">
					<LN0 lnClass="LLN0" inst="" lnType="Siemens_7SJ62/MEAS/LLN0" desc="General">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbC|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbC01" confRev="0" buffered="false" bufTime="100" name="urcbC"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbD|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbD01" confRev="0" buffered="false" bufTime="100" name="urcbD"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbE|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbE01" confRev="0" buffered="false" bufTime="100" name="urcbE"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbF|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbF01" confRev="0" buffered="false" bufTime="100" name="urcbF"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbG|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbG01" confRev="0" buffered="false" bufTime="100" name="urcbG"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbH|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbH01" confRev="0" buffered="false" bufTime="100" name="urcbH"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbI|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbI01" confRev="0" buffered="false" bufTime="100" name="urcbI"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbJ|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$RP$urcbJ01" confRev="0" buffered="false" bufTime="100" name="urcbJ"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">brcbA|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$BR$brcbA01" confRev="0" buffered="true" bufTime="100" name="brcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">brcbB|&lt;ReportControl rptID="Siemens_7SJ62DR/LLN0$BR$brcbB01" confRev="0" buffered="true" bufTime="100" name="brcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
							<DAI name="ldNs">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
						</DOI>
					</LN0>
					<LN prefix="SC_" lnClass="RDRE" inst="1" lnType="Siemens_7SJ62/DR/SC_RDRE1" desc="Recording">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62DR/SC_RDRE1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62DR/SC_RDRE1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="RcdMade" desc="Made" />
						<DOI name="FltNum" desc="Fault number" />
						<DOI name="GriFltNum" desc="Grid fault num" />
						<DOI name="RcdStr" desc="Start" />
					</LN>
					<LN prefix="" lnClass="LPHD" inst="1" lnType="Siemens_7SJ62/PROT/LPHD1" desc="Device">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62DR/LPHD1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62DR/LPHD1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="PhyNam" desc="Info" />
						<DOI name="PhyHealth" desc="State" />
						<DOI name="Proxy" desc="Proxy" />
					</LN>
				</LDevice>
				<LDevice desc="Control" inst="CTRL">
					<LN0 lnClass="LLN0" inst="" lnType="Siemens_7SJ62/CTRL/LLN0" desc="General">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbC|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbC01" confRev="0" buffered="false" bufTime="100" name="urcbC"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbD|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbD01" confRev="0" buffered="false" bufTime="100" name="urcbD"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbE|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbE01" confRev="0" buffered="false" bufTime="100" name="urcbE"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbF|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbF01" confRev="0" buffered="false" bufTime="100" name="urcbF"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbG|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbG01" confRev="0" buffered="false" bufTime="100" name="urcbG"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbH|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbH01" confRev="0" buffered="false" bufTime="100" name="urcbH"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbI|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbI01" confRev="0" buffered="false" bufTime="100" name="urcbI"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbJ|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$RP$urcbJ01" confRev="0" buffered="false" bufTime="100" name="urcbJ"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">brcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$BR$brcbA01" confRev="0" buffered="true" bufTime="100" name="brcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">brcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LLN0$BR$brcbB01" confRev="0" buffered="true" bufTime="100" name="brcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
							<DAI name="ldNs">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="OpTmh" desc="Operating hours" />
						<DOI name="LEDRs" desc="LED acknwldgmnt">
							<DAI name="ctlModel">
								<Val>direct-with-normal-security</Val>
							</DAI>
						</DOI>
					</LN0>
					<LN lnClass="CALH" inst="1" lnType="Siemens_7SJ62/CTRL/CALH1" desc="Fault">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/CALH1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/CALH1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="GrAlm" desc="Group alarms" />
						<DOI name="GrWrn" desc="Group Warning" />
						<DOI name="ErrBoard1" desc="Error Board 1">
							<DAI name="d">
								<Val>ErrBoard1 stands for Error on board 1</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SDx IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="ErrBoard2" desc="Error Board 2">
							<DAI name="d">
								<Val>ErrBoard2 stands for Error on board 2</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SDx IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="ErrBoard3" desc="Error Board 3">
							<DAI name="d">
								<Val>ErrBoard3 stands for Error on board 3</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SDx IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="ErrBoard4" desc="Error Board 4">
							<DAI name="d">
								<Val>ErrBoard4 stands for Error on board 4</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SDx IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="ErrBoard5" desc="Error Board 5">
							<DAI name="d">
								<Val>ErrBoard5 stands for Error on board 5</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SDx IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="ErrBoard6" desc="Error Board 6">
							<DAI name="d">
								<Val>ErrBoard6 stands for Error on board 6</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SDx IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="ErrBoard7" desc="Error Board 7">
							<DAI name="d">
								<Val>ErrBoard7 stands for Error on board 7</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SDx IED Configuration Description File</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="" lnClass="LPHD" inst="1" lnType="Siemens_7SJ62/CTRL/LPHD1" desc="Device">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LPHD1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/LPHD1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="PhyNam" desc="Info" />
						<DOI name="PhyHealth" desc="State" />
						<DOI name="Proxy" desc="Proxy" />
						<DOI name="DevStr" desc="Startup">
							<!-- SIEMENS-spezifisch -->
							<DAI name="d">
								<Val>DevStr stands for either InitialStart or Resume</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SJ6 IED Configuration Description File</Val>
							</DAI>
						</DOI>
						<DOI name="CtlNum" desc="Test oper Nr">
							<!-- SIEMENS-spezifisch -->
							<DAI name="d">
								<Val>CtlNum stands for number of Test Operations</Val>
							</DAI>
							<DAI name="dataNs">
								<Val>7SJ6 IED Configuration Description File</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q0" lnClass="XCBR" inst="1" lnType="Siemens_7SJ62/CTRL/Q0XCBR1" desc="BreakerXCBR">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q0XCBR1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q0XCBR1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="OpCnt" desc="Operat. counter" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkOpn" desc="Open block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkCls" desc="Close block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="CBOpCap" desc="Switch capablty">
							<DAI name="stVal">
								<Val>None</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q0" lnClass="CSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CSWI1" desc="BreakerCSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q0CSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q0CSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>sbo-with-enhanced-security</Val>
							</DAI>
							<DAI name="sboTimeout">
								<Val>300000</Val>
							</DAI>
							<DAI name="sboClass">
								<Val>operate-once</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q0" lnClass="CILO" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CILO1" desc="BreakerCILO">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q0CILO1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q0CILO1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="EnaOpn" desc="Enable Open" />
						<DOI name="EnaCls" desc="Enable Close" />
					</LN>
					<LN prefix="Q1" lnClass="XSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q1XSWI1" desc="Disc.Swit.XSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q1XSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q1XSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="OpCnt" desc="Operat. counter" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkOpn" desc="Open block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkCls" desc="Close block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="SwTyp" desc="Switch type">
							<DAI name="stVal">
								<Val>Disconnector</Val>
							</DAI>
						</DOI>
						<DOI name="SwOpCap" desc="Switch capablty">
							<DAI name="stVal">
								<Val>None</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q1" lnClass="CSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CSWI1" desc="Disc.Swit.CSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q1CSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q1CSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>sbo-with-enhanced-security</Val>
							</DAI>
							<DAI name="sboTimeout">
								<Val>300000</Val>
							</DAI>
							<DAI name="sboClass">
								<Val>operate-once</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q1" lnClass="CILO" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CILO1" desc="Disc.Swit.CILO">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q1CILO1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q1CILO1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="EnaOpn" desc="Enable Open" />
						<DOI name="EnaCls" desc="Enable Close" />
					</LN>
					<LN prefix="Q2" lnClass="XSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q1XSWI1" desc="Q2 Op/ClXSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q2XSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q2XSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="OpCnt" desc="Operat. counter" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkOpn" desc="Open block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkCls" desc="Close block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="SwTyp" desc="Switch type">
							<DAI name="stVal">
								<Val>Disconnector</Val>
							</DAI>
						</DOI>
						<DOI name="SwOpCap" desc="Switch capablty">
							<DAI name="stVal">
								<Val>None</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q2" lnClass="CSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CSWI1" desc="Q2 Op/ClCSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q2CSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q2CSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>sbo-with-enhanced-security</Val>
							</DAI>
							<DAI name="sboTimeout">
								<Val>300000</Val>
							</DAI>
							<DAI name="sboClass">
								<Val>operate-once</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q2" lnClass="CILO" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CILO1" desc="Q2 Op/ClCILO">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q2CILO1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q2CILO1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="EnaOpn" desc="Enable Open" />
						<DOI name="EnaCls" desc="Enable Close" />
					</LN>
					<LN prefix="Q8" lnClass="XSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q1XSWI1" desc="EarthSwitXSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q8XSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q8XSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="OpCnt" desc="Operat. counter" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkOpn" desc="Open block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkCls" desc="Close block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="SwTyp" desc="Switch type">
							<DAI name="stVal">
								<Val>Earthing Switch</Val>
							</DAI>
						</DOI>
						<DOI name="SwOpCap" desc="Switch capablty">
							<DAI name="stVal">
								<Val>None</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q8" lnClass="CSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CSWI1" desc="EarthSwitCSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q8CSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q8CSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>sbo-with-enhanced-security</Val>
							</DAI>
							<DAI name="sboTimeout">
								<Val>300000</Val>
							</DAI>
							<DAI name="sboClass">
								<Val>operate-once</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q8" lnClass="CILO" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CILO1" desc="EarthSwitCILO">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q8CILO1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q8CILO1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="EnaOpn" desc="Enable Open" />
						<DOI name="EnaCls" desc="Enable Close" />
					</LN>
					<LN prefix="Q9" lnClass="XSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q1XSWI1" desc="Q9 Op/ClXSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q9XSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q9XSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="OpCnt" desc="Operat. counter" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkOpn" desc="Open block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="BlkCls" desc="Close block">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="SwTyp" desc="Switch type">
							<DAI name="stVal">
								<Val>Disconnector</Val>
							</DAI>
						</DOI>
						<DOI name="SwOpCap" desc="Switch capablty">
							<DAI name="stVal">
								<Val>None</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q9" lnClass="CSWI" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CSWI1" desc="Q9 Op/ClCSWI">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q9CSWI1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q9CSWI1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="Loc" desc="Control Auth" />
						<DOI name="Pos" desc="Position">
							<DAI name="ctlModel">
								<Val>sbo-with-enhanced-security</Val>
							</DAI>
							<DAI name="sboTimeout">
								<Val>300000</Val>
							</DAI>
							<DAI name="sboClass">
								<Val>operate-once</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="Q9" lnClass="CILO" inst="1" lnType="Siemens_7SJ62/CTRL/Q0CILO1" desc="Q9 Op/ClCILO">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q9CILO1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62CTRL/Q9CILO1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="EnaOpn" desc="Enable Open" />
						<DOI name="EnaCls" desc="Enable Close" />
					</LN>
				</LDevice>
				<LDevice desc="Extended" inst="EXT">
					<LN0 lnClass="LLN0" inst="" lnType="Siemens_7SJ62/MEAS/LLN0" desc="General">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbC|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbC01" confRev="0" buffered="false" bufTime="100" name="urcbC"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbD|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbD01" confRev="0" buffered="false" bufTime="100" name="urcbD"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbE|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbE01" confRev="0" buffered="false" bufTime="100" name="urcbE"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbF|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbF01" confRev="0" buffered="false" bufTime="100" name="urcbF"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbG|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbG01" confRev="0" buffered="false" bufTime="100" name="urcbG"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbH|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbH01" confRev="0" buffered="false" bufTime="100" name="urcbH"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbI|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbI01" confRev="0" buffered="false" bufTime="100" name="urcbI"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbJ|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$RP$urcbJ01" confRev="0" buffered="false" bufTime="100" name="urcbJ"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">brcbA|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$BR$brcbA01" confRev="0" buffered="true" bufTime="100" name="brcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">brcbB|&lt;ReportControl rptID="Siemens_7SJ62EXT/LLN0$BR$brcbB01" confRev="0" buffered="true" bufTime="100" name="brcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="true" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
							<DAI name="ldNs">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
						</DOI>
					</LN0>
					<LN prefix="" lnClass="LPHD" inst="1" lnType="Siemens_7SJ62/PROT/LPHD1" desc="Device">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62EXT/LPHD1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62EXT/LPHD1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="PhyNam" desc="Info" />
						<DOI name="PhyHealth" desc="State" />
						<DOI name="Proxy" desc="Proxy" />
					</LN>
					<LN prefix="pd" lnClass="GGIO" inst="1" lnType="Siemens_7SJ62/EXT/pdGGIO1">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62EXT/pdGGIO1$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62EXT/pdGGIO1$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode" />
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="SPCSO5" desc="&gt;Reset LED">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="pd" lnClass="GGIO" inst="513" lnType="Siemens_7SJ62/EXT/pdGGIO513">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62EXT/pdGGIO513$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62EXT/pdGGIO513$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode" />
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="SPCSO33" desc="&gt;Back Light on">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="pd" lnClass="GGIO" inst="27" lnType="Siemens_7SJ62/EXT/pdGGIO27">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62EXT/pdGGIO27$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62EXT/pdGGIO27$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode" />
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="SPCSO60" desc="&gt;BLOCK IE&gt;&gt;">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="SPCSO57" desc="&gt;BLOCK I&gt;&gt;">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
					</LN>
					<LN prefix="pd" lnClass="GGIO" inst="3" lnType="Siemens_7SJ62/EXT/pdGGIO3">
						<Private type="Siemens-ControlBlockStorage">urcbA|&lt;ReportControl rptID="Siemens_7SJ62EXT/pdGGIO3$RP$urcbA01" confRev="0" buffered="false" bufTime="100" name="urcbA"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<Private type="Siemens-ControlBlockStorage">urcbB|&lt;ReportControl rptID="Siemens_7SJ62EXT/pdGGIO3$RP$urcbB01" confRev="0" buffered="false" bufTime="100" name="urcbB"&gt; &lt;Private type="Siemens-PredefinedControlBlock" /&gt; &lt;TrgOps dchg="true" qchg="true" dupd="true" period="true" /&gt; &lt;OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" configRef="true" /&gt; &lt;RptEnabled max="1" /&gt; &lt;/ReportControl&gt;</Private>
						<DOI name="Mod" desc="Mode" />
						<DOI name="Beh" desc="Behaviour" />
						<DOI name="Health" desc="State" />
						<DOI name="NamPlt" desc="Info">
							<DAI name="configRev">
								<Val>160304190901000</Val>
							</DAI>
						</DOI>
						<DOI name="SPCSO34" desc="Failure: Current Summation">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="SPCSO35" desc="Failure: Current Balance">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
						<DOI name="SPCSO39" desc="Failure: Voltage Balance">
							<DAI name="ctlModel">
								<Val>status-only</Val>
							</DAI>
						</DOI>
					</LN>
				</LDevice>
			</Server>
		</AccessPoint>
	</IED>
	<DataTypeTemplates>
		<LNodeType id="Siemens_7SJ62/PROT/LLN0" lnClass="LLN0">
			<DO name="Mod" type="myMod_0" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_6" />
			<DO name="OpTmh" type="myOpTmh_7" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/PROT/LPHD1" lnClass="LPHD">
			<DO name="PhyNam" type="myPhyNam_9" />
			<DO name="PhyHealth" type="myHealth_5" />
			<DO name="Proxy" type="myProxy_11" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/PROT/PTRC1" lnClass="PTRC">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="Tr" type="myTr_16" />
			<DO name="Str" type="myStr_17" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/PROT/XCBR1" lnClass="XCBR">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="Loc" type="myProxy_11" />
			<DO name="OpCnt" type="myOpTmh_7" />
			<DO name="Pos" type="myPos_24" />
			<DO name="BlkOpn" type="myBlkOpn_25" />
			<DO name="BlkCls" type="myBlkOpn_25" />
			<DO name="SumSwARsA" type="mySumSwARsA_27" />
			<DO name="SumSwARsB" type="mySumSwARsA_27" />
			<DO name="SumSwARsC" type="mySumSwARsA_27" />
			<DO name="CBOpCap" type="myCBOpCap_33" />
			<DO name="SwAphs" type="mySwAphs_34" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/PROT/PTOC6" lnClass="PTOC">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="Str" type="myStr_17" />
			<DO name="Op" type="myTr_16" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/PROT/PTRC2" lnClass="PTRC">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="Op" type="myTr_16" />
			<DO name="Str" type="myStr_82" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/MEAS/LLN0" lnClass="LLN0">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_6" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/MEAS/MMXU1" lnClass="MMXU">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="TotW" type="myTotW_91" />
			<DO name="TotVAr" type="myTotW_91" />
			<DO name="TotVA" type="myTotW_91" />
			<DO name="TotPF" type="myTotW_91" />
			<DO name="Hz" type="myTotW_91" />
			<DO name="PPV" type="myPPV_111" />
			<DO name="PhV" type="myPhV_130" />
			<DO name="A" type="myPhV_130" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/MEAS/MMTR1" lnClass="MMTR">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="SupWh" type="mySupWh_184" />
			<DO name="SupVArh" type="mySupWh_184" />
			<DO name="DmdWh" type="mySupWh_184" />
			<DO name="DmdVArh" type="mySupWh_184" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/MEAS/MSQI1" lnClass="MSQI">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="SeqA" type="mySeqA_196" />
			<DO name="SeqV" type="mySeqA_196" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/DR/SC_RDRE1" lnClass="RDRE">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="RcdMade" type="myProxy_11" />
			<DO name="FltNum" type="myOpTmh_7" />
			<DO name="GriFltNum" type="myOpTmh_7" />
			<DO name="RcdStr" type="myProxy_11" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/CTRL/LLN0" lnClass="LLN0">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_6" />
			<DO name="Loc" type="myProxy_11" />
			<DO name="OpTmh" type="myOpTmh_7" />
			<DO name="LEDRs" type="myLEDRs_258" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/CTRL/CALH1" lnClass="CALH">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="GrAlm" type="myProxy_11" />
			<DO name="GrWrn" type="myProxy_11" />
			<DO name="ErrBoard1" type="myErrBoard1_268" />
			<DO name="ErrBoard2" type="myErrBoard1_268" />
			<DO name="ErrBoard3" type="myErrBoard1_268" />
			<DO name="ErrBoard4" type="myErrBoard1_268" />
			<DO name="ErrBoard5" type="myErrBoard1_268" />
			<DO name="ErrBoard6" type="myErrBoard1_268" />
			<DO name="ErrBoard7" type="myErrBoard1_268" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/CTRL/LPHD1" lnClass="LPHD">
			<DO name="PhyNam" type="myPhyNam_9" />
			<DO name="PhyHealth" type="myHealth_5" />
			<DO name="Proxy" type="myProxy_11" />
			<DO name="DevStr" type="myDevStr_278" />
			<DO name="CtlNum" type="myDevStr_278" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/CTRL/Q0XCBR1" lnClass="XCBR">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="Loc" type="myProxy_11" />
			<DO name="OpCnt" type="myOpTmh_7" />
			<DO name="Pos" type="myPos_24" />
			<DO name="BlkOpn" type="myBlkOpn_25" />
			<DO name="BlkCls" type="myBlkOpn_25" />
			<DO name="CBOpCap" type="myCBOpCap_33" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/CTRL/Q0CSWI1" lnClass="CSWI">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="Loc" type="myProxy_11" />
			<DO name="Pos" type="myPos_295" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/CTRL/Q0CILO1" lnClass="CILO">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="EnaOpn" type="myProxy_11" />
			<DO name="EnaCls" type="myProxy_11" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/CTRL/Q1XSWI1" lnClass="XSWI">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="Loc" type="myProxy_11" />
			<DO name="OpCnt" type="myOpTmh_7" />
			<DO name="Pos" type="myPos_24" />
			<DO name="BlkOpn" type="myBlkOpn_25" />
			<DO name="BlkCls" type="myBlkOpn_25" />
			<DO name="SwTyp" type="mySwTyp_318" />
			<DO name="SwOpCap" type="mySwOpCap_319" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/EXT/pdGGIO1" lnClass="GGIO">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="SPCSO5" type="myBlkOpn_25" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/EXT/pdGGIO513" lnClass="GGIO">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="SPCSO33" type="myBlkOpn_25" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/EXT/pdGGIO27" lnClass="GGIO">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="SPCSO60" type="myBlkOpn_25" />
			<DO name="SPCSO57" type="myBlkOpn_25" />
		</LNodeType>
		<LNodeType id="Siemens_7SJ62/EXT/pdGGIO3" lnClass="GGIO">
			<DO name="Mod" type="myMod_12" />
			<DO name="Beh" type="myBeh_4" />
			<DO name="Health" type="myHealth_5" />
			<DO name="NamPlt" type="myNamPlt_15" />
			<DO name="SPCSO34" type="myBlkOpn_25" />
			<DO name="SPCSO35" type="myBlkOpn_25" />
			<DO name="SPCSO39" type="myBlkOpn_25" />
		</LNodeType>
		<DOType id="myMod_0" cdc="INC">
			<DA fc="ST" name="origin" bType="Struct" type="myorigin_1" />
			<DA fc="ST" name="ctlNum" bType="INT8U" />
			<DA dchg="true" fc="ST" name="stVal" bType="Enum" type="Beh" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="CF" name="ctlModel" bType="Enum" valKind="RO" type="ctlModel" />
			<DA fc="CO" name="Oper" bType="Struct" type="myOper_2" />
		</DOType>
		<DOType id="myBeh_4" cdc="INS">
			<DA dchg="true" fc="ST" name="stVal" bType="Enum" type="Beh" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="myHealth_5" cdc="INS">
			<DA dchg="true" fc="ST" name="stVal" bType="Enum" type="Health" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="myNamPlt_6" cdc="LPL">
			<DA fc="DC" name="vendor" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="swRev" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="d" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="configRev" bType="VisString255" valKind="RO" />
			<DA fc="EX" name="ldNs" bType="VisString255" valKind="RO" />
		</DOType>
		<DOType id="myOpTmh_7" cdc="INS">
			<DA dchg="true" fc="ST" name="stVal" bType="INT32" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="myPhyNam_9" cdc="DPL">
			<DA fc="DC" name="vendor" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="hwRev" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="swRev" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="serNum" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="model" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="location" bType="VisString255" valKind="RO" />
		</DOType>
		<DOType id="myProxy_11" cdc="SPS">
			<DA dchg="true" fc="ST" name="stVal" bType="BOOLEAN" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="myMod_12" cdc="INC">
			<DA dchg="true" fc="ST" name="stVal" bType="Enum" type="Beh" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="CF" name="ctlModel" bType="Enum" valKind="RO" type="ctlModel" />
		</DOType>
		<DOType id="myNamPlt_15" cdc="LPL">
			<DA fc="DC" name="vendor" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="swRev" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="d" bType="VisString255" valKind="RO" />
			<DA fc="DC" name="configRev" bType="VisString255" valKind="RO" />
		</DOType>
		<DOType id="myTr_16" cdc="ACT">
			<DA dchg="true" fc="ST" name="general" bType="BOOLEAN" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="myStr_17" cdc="ACD">
			<DA dchg="true" fc="ST" name="general" bType="BOOLEAN" />
			<DA dchg="true" fc="ST" name="dirGeneral" bType="Enum" type="dir" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="myPos_24" cdc="DPC">
			<DA dchg="true" fc="ST" name="stVal" bType="Dbpos" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="CF" name="ctlModel" bType="Enum" valKind="RO" type="ctlModel" />
		</DOType>
		<DOType id="myBlkOpn_25" cdc="SPC">
			<DA dchg="true" fc="ST" name="stVal" bType="BOOLEAN" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="CF" name="ctlModel" bType="Enum" valKind="RO" type="ctlModel" />
		</DOType>
		<DOType id="mySumSwARsA_27" cdc="BCR">
			<DA dchg="true" fc="ST" name="actVal" bType="INT32" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="CF" name="units" bType="Struct" type="myunits_28" />
			<DA fc="CF" name="pulsQty" bType="FLOAT32" valKind="RO" />
			<DA fc="DC" name="d" bType="VisString255" valKind="RO" />
			<DA fc="EX" name="dataNs" bType="VisString255" valKind="RO" />
		</DOType>
		<DOType id="myCBOpCap_33" cdc="INS">
			<DA dchg="true" fc="ST" name="stVal" bType="Enum" type="CBOpCap" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="mySwAphs_34" cdc="WYE">
			<SDO name="phsA" type="myphsA_35" />
			<SDO name="phsB" type="myphsA_35" />
			<SDO name="phsC" type="myphsA_35" />
		</DOType>
		<DOType id="myphsA_35" cdc="CMV">
			<DA fc="MX" name="cVal" bType="Struct" type="mycVal_36" />
			<DA fc="CF" name="units" bType="Struct" type="myunits_28" />
			<DA qchg="true" fc="MX" name="q" bType="Quality" />
			<DA fc="MX" name="t" bType="Timestamp" />
			<DA fc="CF" name="db" bType="INT32U" valKind="Set" />
		</DOType>
		<DOType id="myStr_82" cdc="ACD">
			<DA dchg="true" fc="ST" name="general" bType="BOOLEAN" />
			<DA dchg="true" fc="ST" name="dirGeneral" bType="Enum" type="dir" />
			<DA dchg="true" fc="ST" name="phsA" bType="BOOLEAN" />
			<DA dchg="true" fc="ST" name="dirPhsA" bType="Enum" type="dirPhs" />
			<DA dchg="true" fc="ST" name="phsB" bType="BOOLEAN" />
			<DA dchg="true" fc="ST" name="dirPhsB" bType="Enum" type="dirPhs" />
			<DA dchg="true" fc="ST" name="phsC" bType="BOOLEAN" />
			<DA dchg="true" fc="ST" name="dirPhsC" bType="Enum" type="dirPhs" />
			<DA dchg="true" fc="ST" name="neut" bType="BOOLEAN" />
			<DA dchg="true" fc="ST" name="dirNeut" bType="Enum" type="dirPhs" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="myTotW_91" cdc="MV">
			<DA fc="MX" name="instMag" bType="Struct" type="mymag_37" />
			<DA fc="MX" name="mag" bType="Struct" type="mymag_37" />
			<DA qchg="true" fc="MX" name="q" bType="Quality" />
			<DA fc="MX" name="t" bType="Timestamp" />
			<DA fc="CF" name="units" bType="Struct" type="myunits_28" />
			<DA fc="CF" name="db" bType="INT32U" valKind="Set" />
		</DOType>
		<DOType id="myPPV_111" cdc="DEL">
			<SDO name="phsAB" type="myphsAB_112" />
			<SDO name="phsBC" type="myphsAB_112" />
			<SDO name="phsCA" type="myphsAB_112" />
		</DOType>
		<DOType id="myphsAB_112" cdc="CMV">
			<DA fc="MX" name="instCVal" bType="Struct" type="mycVal_36" />
			<DA fc="MX" name="cVal" bType="Struct" type="mycVal_36" />
			<DA fc="CF" name="units" bType="Struct" type="myunits_28" />
			<DA qchg="true" fc="MX" name="q" bType="Quality" />
			<DA fc="MX" name="t" bType="Timestamp" />
			<DA fc="CF" name="db" bType="INT32U" valKind="Set" />
		</DOType>
		<DOType id="myPhV_130" cdc="WYE">
			<SDO name="phsA" type="myphsAB_112" />
			<SDO name="phsB" type="myphsAB_112" />
			<SDO name="phsC" type="myphsAB_112" />
			<SDO name="neut" type="myphsAB_112" />
		</DOType>
		<DOType id="mySupWh_184" cdc="BCR">
			<DA dchg="true" fc="ST" name="actVal" bType="INT32" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="CF" name="units" bType="Struct" type="myunits_28" />
			<DA fc="CF" name="pulsQty" bType="FLOAT32" valKind="RO" />
		</DOType>
		<DOType id="mySeqA_196" cdc="SEQ">
			<SDO name="c1" type="myphsAB_112" />
			<SDO name="c2" type="myphsAB_112" />
			<SDO name="c3" type="myphsAB_112" />
			<DA fc="MX" name="seqT" bType="Enum" type="seqT" />
		</DOType>
		<DOType id="myLEDRs_258" cdc="SPC">
			<DA fc="ST" name="origin" bType="Struct" type="myorigin_1" />
			<DA fc="ST" name="ctlNum" bType="INT8U" />
			<DA dchg="true" fc="ST" name="stVal" bType="BOOLEAN" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="CF" name="ctlModel" bType="Enum" valKind="RO" type="ctlModel" />
			<DA fc="CO" name="Oper" bType="Struct" type="myOper_260" />
		</DOType>
		<DOType id="myErrBoard1_268" cdc="SPS">
			<DA dchg="true" fc="ST" name="stVal" bType="BOOLEAN" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="DC" name="d" bType="VisString255" valKind="RO" />
			<DA fc="EX" name="dataNs" bType="VisString255" valKind="RO" />
		</DOType>
		<DOType id="myDevStr_278" cdc="INS">
			<DA dchg="true" fc="ST" name="stVal" bType="INT32" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA fc="DC" name="d" bType="VisString255" valKind="RO" />
			<DA fc="EX" name="dataNs" bType="VisString255" valKind="RO" />
		</DOType>
		<DOType id="myPos_295" cdc="DPC">
			<DA fc="ST" name="origin" bType="Struct" type="myorigin_1" />
			<DA fc="ST" name="ctlNum" bType="INT8U" />
			<DA dchg="true" fc="ST" name="stVal" bType="Dbpos" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
			<DA dchg="true" fc="ST" name="stSeld" bType="BOOLEAN" />
			<DA fc="CF" name="ctlModel" bType="Enum" valKind="RO" type="ctlModel" />
			<DA fc="CF" name="sboTimeout" bType="INT32U" valKind="RO" />
			<DA fc="CF" name="sboClass" bType="Enum" valKind="RO" type="sboClass" />
			<DA fc="CO" name="SBOw" bType="Struct" type="myOper_260" />
			<DA fc="CO" name="Oper" bType="Struct" type="myOper_260" />
			<DA fc="CO" name="Cancel" bType="Struct" type="myCancel_301" />
		</DOType>
		<DOType id="mySwTyp_318" cdc="INS">
			<DA dchg="true" fc="ST" name="stVal" bType="Enum" type="SwTyp" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DOType id="mySwOpCap_319" cdc="INS">
			<DA dchg="true" fc="ST" name="stVal" bType="Enum" type="SwOpCap" />
			<DA qchg="true" fc="ST" name="q" bType="Quality" />
			<DA fc="ST" name="t" bType="Timestamp" />
		</DOType>
		<DAType id="myorigin_1">
			<BDA name="orCat" bType="Enum" type="orCategory" />
			<BDA name="orIdent" bType="Octet64" />
		</DAType>
		<DAType id="myOper_2">
			<BDA name="ctlVal" bType="Enum" type="Beh" />
			<BDA name="origin" bType="Struct" type="myorigin_1" />
			<BDA name="ctlNum" bType="INT8U" />
			<BDA name="T" bType="Timestamp" />
			<BDA name="Test" bType="BOOLEAN" />
			<BDA name="Check" bType="Check" />
		</DAType>
		<DAType id="myunits_28">
			<BDA name="SIUnit" bType="Enum" valKind="RO" type="SIUnit" />
			<BDA name="multiplier" bType="Enum" valKind="RO" type="multiplier" />
		</DAType>
		<DAType id="mycVal_36">
			<BDA name="mag" bType="Struct" type="mymag_37" />
		</DAType>
		<DAType id="mymag_37">
			<BDA name="f" bType="FLOAT32" />
		</DAType>
		<DAType id="myOper_260">
			<BDA name="ctlVal" bType="BOOLEAN" />
			<BDA name="origin" bType="Struct" type="myorigin_1" />
			<BDA name="ctlNum" bType="INT8U" />
			<BDA name="T" bType="Timestamp" />
			<BDA name="Test" bType="BOOLEAN" />
			<BDA name="Check" bType="Check" />
		</DAType>
		<DAType id="myCancel_301">
			<BDA name="ctlVal" bType="BOOLEAN" />
			<BDA name="origin" bType="Struct" type="myorigin_1" />
			<BDA name="ctlNum" bType="INT8U" />
			<BDA name="T" bType="Timestamp" />
			<BDA name="Test" bType="BOOLEAN" />
		</DAType>
		<EnumType id="orCategory">
			<EnumVal ord="0">not-supported</EnumVal>
			<EnumVal ord="1">bay-control</EnumVal>
			<EnumVal ord="2">station-control</EnumVal>
			<EnumVal ord="3">remote-control</EnumVal>
			<EnumVal ord="4">automatic-bay</EnumVal>
			<EnumVal ord="5">automatic-station</EnumVal>
			<EnumVal ord="6">automatic-remote</EnumVal>
			<EnumVal ord="7">maintenance</EnumVal>
			<EnumVal ord="8">process</EnumVal>
		</EnumType>
		<EnumType id="SIUnit">
			<EnumVal ord="1" />
			<EnumVal ord="2">m</EnumVal>
			<EnumVal ord="3">kg</EnumVal>
			<EnumVal ord="4">s</EnumVal>
			<EnumVal ord="5">A</EnumVal>
			<EnumVal ord="6">K</EnumVal>
			<EnumVal ord="7">mol</EnumVal>
			<EnumVal ord="8">cd</EnumVal>
			<EnumVal ord="9">deg</EnumVal>
			<EnumVal ord="10">rad</EnumVal>
			<EnumVal ord="11">sr</EnumVal>
			<EnumVal ord="21">Gy</EnumVal>
			<EnumVal ord="23">°C</EnumVal>
			<EnumVal ord="24">Sv</EnumVal>
			<EnumVal ord="25">F</EnumVal>
			<EnumVal ord="26">C</EnumVal>
			<EnumVal ord="27">S</EnumVal>
			<EnumVal ord="28">H</EnumVal>
			<EnumVal ord="29">V</EnumVal>
			<EnumVal ord="30">ohm</EnumVal>
			<EnumVal ord="31">J</EnumVal>
			<EnumVal ord="32">N</EnumVal>
			<EnumVal ord="33">Hz</EnumVal>
			<EnumVal ord="34">lx</EnumVal>
			<EnumVal ord="35">Lm</EnumVal>
			<EnumVal ord="36">Wb</EnumVal>
			<EnumVal ord="37">T</EnumVal>
			<EnumVal ord="38">W</EnumVal>
			<EnumVal ord="39">Pa</EnumVal>
			<EnumVal ord="41">m²</EnumVal>
			<EnumVal ord="42">m³</EnumVal>
			<EnumVal ord="43">m/s</EnumVal>
			<EnumVal ord="44">m/s²</EnumVal>
			<EnumVal ord="45">m³/s</EnumVal>
			<EnumVal ord="46">m/m³</EnumVal>
			<EnumVal ord="47">M</EnumVal>
			<EnumVal ord="48">kg/m³</EnumVal>
			<EnumVal ord="49">m²/s</EnumVal>
			<EnumVal ord="50">W/m K</EnumVal>
			<EnumVal ord="51">J/K</EnumVal>
			<EnumVal ord="52">ppm</EnumVal>
			<EnumVal ord="53">1/s</EnumVal>
			<EnumVal ord="54">rad/s</EnumVal>
			<EnumVal ord="61">VA</EnumVal>
			<EnumVal ord="62">Watts</EnumVal>
			<EnumVal ord="63">VAr</EnumVal>
			<EnumVal ord="64">phi</EnumVal>
			<EnumVal ord="65">cos(phi)</EnumVal>
			<EnumVal ord="66">Vs</EnumVal>
			<EnumVal ord="67">V²</EnumVal>
			<EnumVal ord="68">As</EnumVal>
			<EnumVal ord="69">A²</EnumVal>
			<EnumVal ord="70">A²t</EnumVal>
			<EnumVal ord="71">VAh</EnumVal>
			<EnumVal ord="72">Wh</EnumVal>
			<EnumVal ord="73">VArh</EnumVal>
			<EnumVal ord="74">V/Hz</EnumVal>
		</EnumType>
		<EnumType id="multiplier">
			<EnumVal ord="-24">y</EnumVal>
			<EnumVal ord="-21">z</EnumVal>
			<EnumVal ord="-18">a</EnumVal>
			<EnumVal ord="-15">f</EnumVal>
			<EnumVal ord="-12">p</EnumVal>
			<EnumVal ord="-9">n</EnumVal>
			<EnumVal ord="-6">µ</EnumVal>
			<EnumVal ord="-3">m</EnumVal>
			<EnumVal ord="-2">c</EnumVal>
			<EnumVal ord="-1">d</EnumVal>
			<EnumVal ord="0" />
			<EnumVal ord="1">da</EnumVal>
			<EnumVal ord="2">h</EnumVal>
			<EnumVal ord="3">k</EnumVal>
			<EnumVal ord="6">M</EnumVal>
			<EnumVal ord="9">G</EnumVal>
			<EnumVal ord="12">T</EnumVal>
			<EnumVal ord="15">P</EnumVal>
			<EnumVal ord="18">E</EnumVal>
			<EnumVal ord="21">Z</EnumVal>
			<EnumVal ord="24">Y</EnumVal>
		</EnumType>
		<EnumType id="ctlModel">
			<EnumVal ord="0">status-only</EnumVal>
			<EnumVal ord="1">direct-with-normal-security</EnumVal>
			<EnumVal ord="2">sbo-with-normal-security</EnumVal>
			<EnumVal ord="3">direct-with-enhanced-security</EnumVal>
			<EnumVal ord="4">sbo-with-enhanced-security</EnumVal>
		</EnumType>
		<EnumType id="sboClass">
			<EnumVal ord="0">operate-once</EnumVal>
			<EnumVal ord="1">operate-many</EnumVal>
		</EnumType>
		<EnumType id="dir">
			<EnumVal ord="0">unknown</EnumVal>
			<EnumVal ord="1">forward</EnumVal>
			<EnumVal ord="2">backward</EnumVal>
			<EnumVal ord="3">both</EnumVal>
		</EnumType>
		<EnumType id="dirPhs">
			<EnumVal ord="0">unknown</EnumVal>
			<EnumVal ord="1">forward</EnumVal>
			<EnumVal ord="2">backward</EnumVal>
		</EnumType>
		<EnumType id="seqT">
			<EnumVal ord="0">pos-neg-zero</EnumVal>
			<EnumVal ord="1">dir-quad-zero</EnumVal>
		</EnumType>
		<EnumType id="Beh">
			<EnumVal ord="1">on</EnumVal>
			<EnumVal ord="2">blocked</EnumVal>
			<EnumVal ord="3">test</EnumVal>
			<EnumVal ord="4">test/blocked</EnumVal>
			<EnumVal ord="5">off</EnumVal>
		</EnumType>
		<EnumType id="CBOpCap">
			<EnumVal ord="1">None</EnumVal>
			<EnumVal ord="2">Open</EnumVal>
			<EnumVal ord="3">Close-Open</EnumVal>
			<EnumVal ord="4">Open-Close-Open</EnumVal>
			<EnumVal ord="5">Close-Open-Close-Open</EnumVal>
		</EnumType>
		<EnumType id="Health">
			<EnumVal ord="1">Ok</EnumVal>
			<EnumVal ord="2">Warning</EnumVal>
			<EnumVal ord="3">Alarm</EnumVal>
		</EnumType>
		<EnumType id="SwTyp">
			<EnumVal ord="1">Load Break</EnumVal>
			<EnumVal ord="2">Disconnector</EnumVal>
			<EnumVal ord="3">Earthing Switch</EnumVal>
			<EnumVal ord="4">High Speed Earthing Switch</EnumVal>
		</EnumType>
		<EnumType id="SwOpCap">
			<EnumVal ord="1">None</EnumVal>
			<EnumVal ord="2">Open</EnumVal>
			<EnumVal ord="3">Close</EnumVal>
			<EnumVal ord="4">Open and Close</EnumVal>
		</EnumType>
	</DataTypeTemplates>
</SCL>