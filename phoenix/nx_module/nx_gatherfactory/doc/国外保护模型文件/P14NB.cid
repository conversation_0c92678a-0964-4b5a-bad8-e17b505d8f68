<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2013 (http://www.altova.com) by  () -->
<SCL xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:schemaLocation="http://www.iec.ch/61850/2003/SCL SCL.xsd" xmlns="http://www.iec.ch/61850/2003/SCL">
	<Header id="P14NB" version="3.0" revision="" toolID="ALSTOM IEC61850 Px40 Modelling Tool" nameStructure="IEDName">
		<Text source="">IED Capability Description for P14NB Feeder Management Protection IED - Non Directional (Base)</Text>
	</Header>
	<Communication>
		<SubNetwork name="NONE" type="8-MMS">
			<ConnectedAP iedName="P14NB" apName="AP1">
				<Address>
					<P type="IP">**********</P>
					<P type="IP-SUBNET">***********</P>
					<P type="IP-GATEWAY">**********</P>
					<P type="OSI-PSEL">00000001</P>
					<P type="OSI-SSEL">0001</P>
					<P type="OSI-TSEL">0001</P>
				</Address>
				<GSE ldInst="System" cbName="gcb01">
					<Address>
						<P type="MAC-Address">01-0C-CD-01-00-00</P>
						<P type="APPID">0000</P>
						<P type="VLAN-ID">000</P>
						<P type="VLAN-PRIORITY">4</P>
					</Address>
					<MinTime unit="s" multiplier="m">10</MinTime>
					<MaxTime unit="s" multiplier="m">1000</MaxTime>
				</GSE>
				<GSE ldInst="System" cbName="gcb02">
					<Address>
						<P type="MAC-Address">01-0C-CD-01-00-00</P>
						<P type="APPID">0000</P>
						<P type="VLAN-ID">000</P>
						<P type="VLAN-PRIORITY">4</P>
					</Address>
					<MinTime unit="s" multiplier="m">10</MinTime>
					<MaxTime unit="s" multiplier="m">1000</MaxTime>
				</GSE>
				<GSE ldInst="System" cbName="gcb03">
					<Address>
						<P type="MAC-Address">01-0C-CD-01-00-00</P>
						<P type="APPID">0000</P>
						<P type="VLAN-ID">000</P>
						<P type="VLAN-PRIORITY">4</P>
					</Address>
					<MinTime unit="s" multiplier="m">10</MinTime>
					<MaxTime unit="s" multiplier="m">1000</MaxTime>
				</GSE>
				<GSE ldInst="System" cbName="gcb04">
					<Address>
						<P type="MAC-Address">01-0C-CD-01-00-00</P>
						<P type="APPID">0000</P>
						<P type="VLAN-ID">000</P>
						<P type="VLAN-PRIORITY">4</P>
					</Address>
					<MinTime unit="s" multiplier="m">10</MinTime>
					<MaxTime unit="s" multiplier="m">1000</MaxTime>
				</GSE>
				<GSE ldInst="System" cbName="gcb05">
					<Address>
						<P type="MAC-Address">01-0C-CD-01-00-00</P>
						<P type="APPID">0000</P>
						<P type="VLAN-ID">000</P>
						<P type="VLAN-PRIORITY">4</P>
					</Address>
					<MinTime unit="s" multiplier="m">10</MinTime>
					<MaxTime unit="s" multiplier="m">1000</MaxTime>
				</GSE>
				<GSE ldInst="System" cbName="gcb06">
					<Address>
						<P type="MAC-Address">01-0C-CD-01-00-00</P>
						<P type="APPID">0000</P>
						<P type="VLAN-ID">000</P>
						<P type="VLAN-PRIORITY">4</P>
					</Address>
					<MinTime unit="s" multiplier="m">10</MinTime>
					<MaxTime unit="s" multiplier="m">1000</MaxTime>
				</GSE>
				<GSE ldInst="System" cbName="gcb07">
					<Address>
						<P type="MAC-Address">01-0C-CD-01-00-00</P>
						<P type="APPID">0000</P>
						<P type="VLAN-ID">000</P>
						<P type="VLAN-PRIORITY">4</P>
					</Address>
					<MinTime unit="s" multiplier="m">10</MinTime>
					<MaxTime unit="s" multiplier="m">1000</MaxTime>
				</GSE>
				<GSE ldInst="System" cbName="gcb08">
					<Address>
						<P type="MAC-Address">01-0C-CD-01-00-00</P>
						<P type="APPID">0000</P>
						<P type="VLAN-ID">000</P>
						<P type="VLAN-PRIORITY">4</P>
					</Address>
					<MinTime unit="s" multiplier="m">10</MinTime>
					<MaxTime unit="s" multiplier="m">1000</MaxTime>
				</GSE>
				<PhysConn type="Plug">
					<P type="Type">FOC</P>
					<P type="Plug">ST</P>
				</PhysConn>
			</ConnectedAP>
			<ConnectedAP iedName="CLOCK1" apName="CLOCK1">
				<Address>
					<P type="IP">************</P>
					<P type="IP-SUBNET">0.0.0.0</P>
				</Address>
			</ConnectedAP>
			<ConnectedAP iedName="CLOCK2" apName="CLOCK2">
				<Address>
					<P type="IP">************</P>
					<P type="IP-SUBNET">0.0.0.0</P>
				</Address>
			</ConnectedAP>
		</SubNetwork>
	</Communication>
	<IED name="P14NB" desc="P14NB Feeder Management Protection IED - Non Directional (Base)" type="P14NB" manufacturer="ALSTOM" configVersion="P14NBv50J">
		<Private type="MiCOM-ModellingToolID">ALSTOM IEC61850 Px40 Modelling Tool</Private>
		<Private type="MiCOM-ModellingToolVersion">3.0</Private>
		<Private type="MiCOM-ModelNumber">P14NB??????50?* (J)</Private>
		<Private type="MiCOM-SNTPServers">2</Private>
		<Private type="MiCOM-TimeServer"/>
		<Private type="MiCOM-MCLVersion">V2.0</Private>
		<Private type="MiCOM-UniqueID">20190424-057533170.731-6565</Private>
		<Services>
			<DynAssociation/>
			<SettingGroups/>
			<GetDirectory/>
			<GetDataObjectDefinition/>
			<DataObjectDirectory/>
			<GetDataSetValue/>
			<DataSetDirectory/>
			<ConfDataSet max="100" modify="true"/>
			<ReadWrite/>
			<ConfReportControl max="24"/>
			<GetCBValues/>
			<ReportSettings cbName="Fix" datSet="Conf" rptID="Dyn" optFields="Dyn" bufTime="Dyn" trgOps="Dyn" intgPd="Dyn"/>
			<GSESettings cbName="Fix" datSet="Conf" appID="Conf"/>
			<GOOSE max="8"/>
			<FileHandling/>
			<ConfLNs fixPrefix="true" fixLnInst="true"/>
		</Services>
		<AccessPoint name="AP1" desc="IEC61850 Interface" router="false" clock="false">
			<Server desc="IEC61850 Compliant data model" timeout="900">
				<Authentication none="true"/>
				<LDevice desc="P14NB Controls Domain" inst="Control">
					<LN0 desc="Controls Logical Device" lnType="LLN0_STANDARD" inst="" lnClass="LLN0">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="Returns the Configuration Revision of the Physical Device." name="configRev" valKind="RO"/>
							<DAI name="d" valKind="RO">
								<Val>Controls Logical Device</Val>
							</DAI>
							<DAI name="ldNs" valKind="RO">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN0>
					<LN desc="XCBR Interlocking" lnType="CILO_INTERLOCK" lnClass="CILO" inst="1">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="EnaCls">
							<DAI desc="Returns the status of DDB_BLOCK_REMOTE_CB_OPS" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="EnaOpn">
							<DAI desc="Returns the status of DDB_BLOCK_REMOTE_CB_OPS" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>direct-with-normal-security</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[06 00] - CB CONDITION" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Physical Device Information" lnType="LPHD_STANDARD" lnClass="LPHD" inst="1">
						<DOI name="PhyHealth">
							<DAI desc="Returns the Health of the Physical Device." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="PhyNam">
							<DAI desc="Returns the Hardware Revision of the Physical Device." name="hwRev" valKind="RO"/>
							<DAI desc="[00 05] - Plant Reference" name="location" valKind="RO"/>
							<DAI desc="[00 06] - Model Number" name="model" valKind="RO"/>
							<DAI desc="[00 08] - Serial Number" name="serNum" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Proxy">
							<DAI name="d" valKind="RO">
								<Val>Logical Device is a Proxy</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="PwrUp">
							<DAI name="d" valKind="RO">
								<Val>Px40 Power-Up Detected</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>true</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Circuit Breaker Monitoring" lnType="XCBR_BASIC" lnClass="XCBR" inst="1">
						<Private type="MiCOM-ConfigurableAttributes">Pos.ctlModel,Pos.sboTimeout,Lock.ctlModel,Lock.sboTimeout,</Private>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="BlkCls">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="BlkOpn">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="CBOpCap">
							<DAI desc="Returns CB operating capacity" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="EEHealth">
							<DAI desc="Returns the Health of the CB with respect to XCBR.EEHealth." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Loc">
							<DAI desc="[07 01] - CB Control by" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Lock">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="Handle Circuit Breaker locks" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[06 00] - CB CONDITION" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="OpCnt">
							<DAI desc="[06 01] - CB Operations" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Pos">
							<DAI name="cdcName" valKind="RO">
								<Val>DPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[00 10] - CB Trip/Close" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="Returns the Status of the CB with respect to XCBR.Pos." name="stVal" valKind="RO"/>
						</DOI>
					</LN>
				</LDevice>
				<LDevice desc="P14NB Measurements Domain" inst="Measurements">
					<LN0 desc="Measurements Logical Device" lnType="LLN0_STANDARD" inst="" lnClass="LLN0">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="Returns the Configuration Revision of the Physical Device." name="configRev" valKind="RO"/>
							<DAI name="d" valKind="RO">
								<Val>Measurements Logical Device</Val>
							</DAI>
							<DAI name="ldNs" valKind="RO">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN0>
					<LN desc="Physical Device Information" lnType="LPHD_STANDARD" lnClass="LPHD" inst="1">
						<DOI name="PhyHealth">
							<DAI desc="Returns the Health of the Physical Device." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="PhyNam">
							<DAI desc="Returns the Hardware Revision of the Physical Device." name="hwRev" valKind="RO"/>
							<DAI desc="[00 05] - Plant Reference" name="location" valKind="RO"/>
							<DAI desc="[00 06] - Model Number" name="model" valKind="RO"/>
							<DAI desc="[00 08] - Serial Number" name="serNum" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Proxy">
							<DAI name="d" valKind="RO">
								<Val>Logical Device is a Proxy</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="PwrUp">
							<DAI name="d" valKind="RO">
								<Val>Px40 Power-Up Detected</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>true</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Primary Fourier Measurements (No Volts)" lnType="MMXU_FOURIER_P14N" lnClass="MMXU" inst="1" prefix="PriFou">
						<Private type="MiCOM-ConfigurableAttributes">Hz.units.multiplier,Hz.db,Hz.rangeC.min.f,Hz.rangeC.max.f,A1.phsA.units.multiplier,A1.phsA.db,A1.phsA.rangeC.min.f,A1.phsA.rangeC.max.f,A1.phsB.units.multiplier,A1.phsB.db,A1.phsB.rangeC.min.f,A1.phsB.rangeC.max.f,A1.phsC.units.multiplier,A1.phsC.db,A1.phsC.rangeC.min.f,A1.phsC.rangeC.max.f,A1.neut.units.multiplier,A1.neut.db,A1.neut.rangeC.min.f,A1.neut.rangeC.max.f,A2.res.units.multiplier,A2.res.db,A2.res.rangeC.min.f,A2.res.rangeC.max.f,A3.res.units.multiplier,A3.res.db,A3.res.rangeC.min.f,A3.res.rangeC.max.f,</Private>
						<DOI name="A1">
							<DAI name="d" valKind="RO">
								<Val>Fourier Based Measurements</Val>
							</DAI>
							<SDI name="neut">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 0A] - IN Derived Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 09] - IN Derived Mag" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 09] - IN Derived Mag" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsA">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 02] - IA Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 01] - IA Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 01] - IA Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsB">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 04] - IB Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 03] - IB Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 03] - IB Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsC">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 06] - IC Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 05] - IC Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 05] - IC Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
						</DOI>
						<DOI name="A2">
							<DAI name="d" valKind="RO">
								<Val>IN Measured Measurement</Val>
							</DAI>
							<SDI name="res">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 08] - IN Measured Ang" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 07] - IN Measured Mag" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 07] - IN Measured Mag" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
						</DOI>
						<DOI name="A3">
							<DAI name="d" valKind="RO">
								<Val>ISEF Measurements</Val>
							</DAI>
							<SDI name="res">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 0C] - ISEF Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 0B] - ISEF Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>2</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 0B] - ISEF Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
						</DOI>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Hz">
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[02 2D] - Frequency" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>70</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>40</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[02 2D] - Frequency" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Fourier Based Measurements</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Primary Sequence Measurements (No Volts)" lnType="MSQI_ALL_P14N" lnClass="MSQI" inst="1" prefix="Pri">
						<Private type="MiCOM-ConfigurableAttributes">SeqA.c1.units.multiplier,SeqA.c1.db,SeqA.c1.rangeC.min.f,SeqA.c1.rangeC.max.f,SeqA.c2.units.multiplier,SeqA.c2.db,SeqA.c2.rangeC.min.f,SeqA.c2.rangeC.max.f,SeqA.c3.units.multiplier,SeqA.c3.db,SeqA.c3.rangeC.min.f,SeqA.c3.rangeC.max.f,ImbNgA.units.multiplier,ImbNgA.db,ImbNgA.rangeC.min.f,ImbNgA.rangeC.max.f,</Private>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="ImbNgA">
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[04 0C] - I2/I1 Ratio" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[04 0C] - I2/I1 Ratio" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Sequence and Imbalance Measurements</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="SeqA">
							<SDI name="c1">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 41] - I1 Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 40] - I1 Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 40] - I1 Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="c2">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 43] - I2 Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 42] - I2 Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 42] - I2 Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="c3">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 45] - I0 Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 44] - I0 Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 44] - I0 Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<DAI name="seqT" valKind="RO">
								<Val>pos-neg-zero</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Primary Metering Statistics (No Volts)" lnType="MSTA_I_W_VAR_P14N" lnClass="MSTA" inst="1" prefix="Pri">
						<Private type="MiCOM-ConfigurableAttributes">AvAmps1.units.multiplier,AvAmps1.db,AvAmps1.rangeC.min.f,AvAmps1.rangeC.max.f,AvAmps2.units.multiplier,AvAmps2.db,AvAmps2.rangeC.min.f,AvAmps2.rangeC.max.f,AvAmps3.units.multiplier,AvAmps3.db,AvAmps3.rangeC.min.f,AvAmps3.rangeC.max.f,AvAmps4.units.multiplier,AvAmps4.db,AvAmps4.rangeC.min.f,AvAmps4.rangeC.max.f,AvAmps5.units.multiplier,AvAmps5.db,AvAmps5.rangeC.min.f,AvAmps5.rangeC.max.f,AvAmps6.units.multiplier,AvAmps6.db,AvAmps6.rangeC.min.f,AvAmps6.rangeC.max.f,MaxAmps1.units.multiplier,MaxAmps1.db,MaxAmps1.rangeC.min.f,MaxAmps1.rangeC.max.f,MaxAmps2.units.multiplier,MaxAmps2.db,MaxAmps2.rangeC.min.f,MaxAmps2.rangeC.max.f,MaxAmps3.units.multiplier,MaxAmps3.db,MaxAmps3.rangeC.min.f,MaxAmps3.rangeC.max.f,</Private>
						<DOI name="AvAmps1">
							<DAI desc="[03 18] - IA  Fixed Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 18] - IA  Fixed Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 18] - IA  Fixed Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps2">
							<DAI desc="[03 19] - IB  Fixed Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 19] - IB  Fixed Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 19] - IB  Fixed Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps3">
							<DAI desc="[03 1A] - IC  Fixed Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 1A] - IC  Fixed Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 1A] - IC  Fixed Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps4">
							<DAI desc="[03 1D] - IA Roll Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 1D] - IA Roll Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 1D] - IA Roll Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps5">
							<DAI desc="[03 1E] - IB Roll Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 1E] - IB Roll Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 1E] - IB Roll Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps6">
							<DAI desc="[03 1F] - IC Roll Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 1F] - IC Roll Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 1F] - IC Roll Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="MaxAmps1">
							<DAI desc="[03 22] - IA Peak Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 22] - IA Peak Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 22] - IA Peak Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="MaxAmps2">
							<DAI desc="[03 23] - IB Peak Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 23] - IB Peak Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 23] - IB Peak Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="MaxAmps3">
							<DAI desc="[03 24] - IC Peak Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 24] - IC Peak Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 24] - IC Peak Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Metering Statistics</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Primary RMS Measurements (No Volts)" lnType="MMXU_RMS_P14N" lnClass="MMXU" inst="1" prefix="PriRms">
						<Private type="MiCOM-ConfigurableAttributes">A.phsA.units.multiplier,A.phsA.db,A.phsA.rangeC.min.f,A.phsA.rangeC.max.f,A.phsB.units.multiplier,A.phsB.db,A.phsB.rangeC.min.f,A.phsB.rangeC.max.f,A.phsC.units.multiplier,A.phsC.db,A.phsC.rangeC.min.f,A.phsC.rangeC.max.f,</Private>
						<DOI name="A">
							<DAI name="d" valKind="Set">
								<Val>RMS Measurements</Val>
							</DAI>
							<SDI name="phsA">
								<SDI name="cVal">
									<SDI name="mag">
										<DAI desc="[02 10] - IA RMS" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 10] - IA RMS" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsB">
								<SDI name="cVal">
									<SDI name="mag">
										<DAI desc="[02 11] - IB RMS" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 11] - IB RMS" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsC">
								<SDI name="cVal">
									<SDI name="mag">
										<DAI desc="[02 12] - IC RMS" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 11] - IB RMS" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
						</DOI>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>RMS Measurements</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Secondary Fourier Measurements (No Volts)" lnType="MMXU_FOURIER_P14N" lnClass="MMXU" inst="1" prefix="SecFou">
						<Private type="MiCOM-ConfigurableAttributes">Hz.units.multiplier,Hz.db,Hz.rangeC.min.f,Hz.rangeC.max.f,A1.phsA.units.multiplier,A1.phsA.db,A1.phsA.rangeC.min.f,A1.phsA.rangeC.max.f,A1.phsB.units.multiplier,A1.phsB.db,A1.phsB.rangeC.min.f,A1.phsB.rangeC.max.f,A1.phsC.units.multiplier,A1.phsC.db,A1.phsC.rangeC.min.f,A1.phsC.rangeC.max.f,A1.neut.units.multiplier,A1.neut.db,A1.neut.rangeC.min.f,A1.neut.rangeC.max.f,A2.res.units.multiplier,A2.res.db,A2.res.rangeC.min.f,A2.res.rangeC.max.f,A3.res.units.multiplier,A3.res.db,A3.res.rangeC.min.f,A3.res.rangeC.max.f,</Private>
						<DOI name="A1">
							<DAI name="d" valKind="RO">
								<Val>Fourier Based Measurements</Val>
							</DAI>
							<SDI name="neut">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 0A] - IN Derived Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 09] - IN Derived Mag" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 09] - IN Derived Mag" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsA">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 02] - IA Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 01] - IA Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 01] - IA Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsB">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 04] - IB Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 03] - IB Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 03] - IB Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsC">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 06] - IC Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 05] - IC Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 05] - IC Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
						</DOI>
						<DOI name="A2">
							<DAI name="d" valKind="RO">
								<Val>IN Measured Measurement</Val>
							</DAI>
							<SDI name="res">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 08] - IN Measured Ang" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 07] - IN Measured Mag" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 07] - IN Measured Mag" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
						</DOI>
						<DOI name="A3">
							<DAI name="d" valKind="RO">
								<Val>ISEF Measurements</Val>
							</DAI>
							<SDI name="res">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 0C] - ISEF Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 0B] - ISEF Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>2</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 0B] - ISEF Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
						</DOI>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Hz">
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[02 2D] - Frequency" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>70</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>40</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[02 2D] - Frequency" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Fourier Based Measurements</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Secondary based metering quantities (No Volts)" lnType="MSQI_ALL_P14N" lnClass="MSQI" inst="1" prefix="Sec">
						<Private type="MiCOM-ConfigurableAttributes">SeqA.c1.units.multiplier,SeqA.c1.db,SeqA.c1.rangeC.min.f,SeqA.c1.rangeC.max.f,SeqA.c2.units.multiplier,SeqA.c2.db,SeqA.c2.rangeC.min.f,SeqA.c2.rangeC.max.f,SeqA.c3.units.multiplier,SeqA.c3.db,SeqA.c3.rangeC.min.f,SeqA.c3.rangeC.max.f,ImbNgA.units.multiplier,ImbNgA.db,ImbNgA.rangeC.min.f,ImbNgA.rangeC.max.f,</Private>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="ImbNgA">
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[04 0C] - I2/I1 Ratio" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[04 0C] - I2/I1 Ratio" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Sequence and Imbalance Measurements</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="SeqA">
							<SDI name="c1">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 41] - I1 Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 40] - I1 Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 40] - I1 Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="c2">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 43] - I2 Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 42] - I2 Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 42] - I2 Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="c3">
								<SDI name="cVal">
									<SDI name="ang">
										<DAI desc="[02 45] - I0 Phase Angle" name="f" valKind="RO"/>
									</SDI>
									<SDI name="mag">
										<DAI desc="[02 44] - I0 Magnitude" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 44] - I0 Magnitude" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<DAI name="seqT" valKind="RO">
								<Val>pos-neg-zero</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Secondary Metering Statistics (No Volts)" lnType="MSTA_I_W_VAR_P14N" lnClass="MSTA" inst="1" prefix="Sec">
						<Private type="MiCOM-ConfigurableAttributes">AvAmps1.units.multiplier,AvAmps1.db,AvAmps1.rangeC.min.f,AvAmps1.rangeC.max.f,AvAmps2.units.multiplier,AvAmps2.db,AvAmps2.rangeC.min.f,AvAmps2.rangeC.max.f,AvAmps3.units.multiplier,AvAmps3.db,AvAmps3.rangeC.min.f,AvAmps3.rangeC.max.f,AvAmps4.units.multiplier,AvAmps4.db,AvAmps4.rangeC.min.f,AvAmps4.rangeC.max.f,AvAmps5.units.multiplier,AvAmps5.db,AvAmps5.rangeC.min.f,AvAmps5.rangeC.max.f,AvAmps6.units.multiplier,AvAmps6.db,AvAmps6.rangeC.min.f,AvAmps6.rangeC.max.f,MaxAmps1.units.multiplier,MaxAmps1.db,MaxAmps1.rangeC.min.f,MaxAmps1.rangeC.max.f,MaxAmps2.units.multiplier,MaxAmps2.db,MaxAmps2.rangeC.min.f,MaxAmps2.rangeC.max.f,MaxAmps3.units.multiplier,MaxAmps3.db,MaxAmps3.rangeC.min.f,MaxAmps3.rangeC.max.f,</Private>
						<DOI name="AvAmps1">
							<DAI desc="[03 18] - IA  Fixed Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 18] - IA  Fixed Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 18] - IA  Fixed Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps2">
							<DAI desc="[03 19] - IB  Fixed Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 19] - IB  Fixed Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 19] - IB  Fixed Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps3">
							<DAI desc="[03 1A] - IC  Fixed Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 1A] - IC  Fixed Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 1A] - IC  Fixed Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps4">
							<DAI desc="[03 1D] - IA Roll Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 1D] - IA Roll Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 1D] - IA Roll Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps5">
							<DAI desc="[03 1E] - IB Roll Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 1E] - IB Roll Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 1E] - IB Roll Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="AvAmps6">
							<DAI desc="[03 1F] - IC Roll Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 1F] - IC Roll Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 1F] - IC Roll Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="MaxAmps1">
							<DAI desc="[03 22] - IA Peak Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 22] - IA Peak Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 22] - IA Peak Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="MaxAmps2">
							<DAI desc="[03 23] - IB Peak Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 23] - IB Peak Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 23] - IB Peak Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="MaxAmps3">
							<DAI desc="[03 24] - IC Peak Demand" name="d" valKind="Set"/>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[03 24] - IC Peak Demand" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[03 24] - IC Peak Demand" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Metering Statistics</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Secondary RMS Measurements (No Volts)" lnType="MMXU_RMS_P14N" lnClass="MMXU" inst="1" prefix="SecRms">
						<Private type="MiCOM-ConfigurableAttributes">A.phsA.units.multiplier,A.phsA.db,A.phsA.rangeC.min.f,A.phsA.rangeC.max.f,A.phsB.units.multiplier,A.phsB.db,A.phsB.rangeC.min.f,A.phsB.rangeC.max.f,A.phsC.units.multiplier,A.phsC.db,A.phsC.rangeC.min.f,A.phsC.rangeC.max.f,</Private>
						<DOI name="A">
							<DAI name="d" valKind="Set">
								<Val>RMS Measurements</Val>
							</DAI>
							<SDI name="phsA">
								<SDI name="cVal">
									<SDI name="mag">
										<DAI desc="[02 10] - IA RMS" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 10] - IA RMS" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsB">
								<SDI name="cVal">
									<SDI name="mag">
										<DAI desc="[02 11] - IB RMS" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 11] - IB RMS" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
							<SDI name="phsC">
								<SDI name="cVal">
									<SDI name="mag">
										<DAI desc="[02 12] - IC RMS" name="f" valKind="RO"/>
									</SDI>
								</SDI>
								<DAI name="db" valKind="Set">
									<Val>1000</Val>
								</DAI>
								<SDI name="rangeC">
									<SDI name="hhLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="hLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="lLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="llLim">
										<DAI name="f" valKind="RO">
											<Val>0</Val>
										</DAI>
									</SDI>
									<SDI name="max">
										<DAI name="f" valKind="Set">
											<Val>3</Val>
										</DAI>
									</SDI>
									<SDI name="min">
										<DAI name="f" valKind="Set">
											<Val>0</Val>
										</DAI>
									</SDI>
								</SDI>
								<SDI name="units">
									<DAI name="multiplier" valKind="Set">
										<Val/>
									</DAI>
									<DAI desc="[02 11] - IB RMS" name="SIUnit" valKind="RO"/>
								</SDI>
							</SDI>
						</DOI>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>RMS Measurements</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
				</LDevice>
				<LDevice desc="P14NB Protection Domain" inst="Protection">
					<LN0 desc="Protection Logical device" lnType="LLN0_PROT_P14N" inst="" lnClass="LLN0">
						<Private type="MiCOM-ConfigurableAttributes">OcpMod.ctlModel,NgcMod.ctlModel,EfmMod.ctlModel,EfdMod.ctlModel,SenMod.ctlModel,ThmMod.ctlModel,CbfMod.ctlModel,</Private>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="CbfBeh">
							<DAI desc="[09 20] - CB Fail" name="d" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 20] - CB Fail" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="CbfMod">
							<DAI name="cdcName" valKind="RO">
								<Val>INC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[09 20] - CB Fail" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 20] - CB Fail" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="EfdBeh">
							<DAI desc="[09 14] - Earth Fault 2" name="d" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 14] - Earth Fault 2" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="EfdMod">
							<DAI name="cdcName" valKind="RO">
								<Val>INC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[09 14] - Earth Fault 2" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 14] - Earth Fault 2" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="EfmBeh">
							<DAI desc="[09 13] - Earth Fault 1" name="d" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 13] - Earth Fault 1" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="EfmMod">
							<DAI name="cdcName" valKind="RO">
								<Val>INC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[09 13] - Earth Fault 1" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 13] - Earth Fault 1" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="Returns the Configuration Revision of the Physical Device." name="configRev" valKind="RO"/>
							<DAI name="d" valKind="RO">
								<Val>Protection Logical Device</Val>
							</DAI>
							<DAI name="ldNs" valKind="RO">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="NgcBeh">
							<DAI desc="[09 11] - Neg Sequence O/C" name="d" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 11] - Neg Sequence O/C" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NgcMod">
							<DAI name="cdcName" valKind="RO">
								<Val>INC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[09 11] - Neg Sequence O/C" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 11] - Neg Sequence O/C" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="OcpBeh">
							<DAI desc="[09 10] - Overcurrent" name="d" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 10] - Overcurrent" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="OcpMod">
							<DAI name="cdcName" valKind="RO">
								<Val>INC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[09 10] - Overcurrent" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 10] - Overcurrent" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SenBeh">
							<DAI desc="[09 15] - SEF/REF Prot'n" name="d" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 15] - SEF/REF Prot'n" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SenMod">
							<DAI name="cdcName" valKind="RO">
								<Val>INC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[09 15] - SEF/REF Prot'n" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 15] - SEF/REF Prot'n" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="ThmBeh">
							<DAI desc="[09 17] - Thermal Overload" name="d" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 17] - Thermal Overload" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="ThmMod">
							<DAI name="cdcName" valKind="RO">
								<Val>INC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[09 17] - Thermal Overload" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="[09 17] - Thermal Overload" name="stVal" valKind="RO"/>
						</DOI>
					</LN0>
					<LN desc="CB Fail 1" lnType="RBRF_EXTTRIP" lnClass="RBRF" inst="1" prefix="Cbf">
						<DOI name="Beh">
							<DAI desc="[FE 07] - CbfRBRF1.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[45 02] - CB Fail 1 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[45 02] - CB Fail 1 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="OpEx">
							<DAI desc="DDB Ordinal: 353" name="general" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="CB Fail 2" lnType="RBRF_EXTTRIP" lnClass="RBRF" inst="2" prefix="Cbf">
						<DOI name="Beh">
							<DAI desc="[FE 08] - CbfRBRF2.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[45 04] - CB Fail 2 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[45 04] - CB Fail 2 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="OpEx">
							<DAI desc="DDB Ordinal: 354" name="general" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="IN2&gt; 1 Earth Fault (Derived)" lnType="PTOC_NEU" lnClass="PTOC" inst="1" prefix="Efd">
						<DOI name="Beh">
							<DAI desc="[FE 0D] - EfdPTOC1.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[39 25] - IN2&gt;1 Function" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[39 25] - IN2&gt;1 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 265" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 265" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[39 26] - IN2&gt;1 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[39 26] - IN2&gt;1 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 319" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 319" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="IN2&gt; 2 Earth Fault (Derived)" lnType="PTOC_NEU" lnClass="PTOC" inst="2" prefix="Efd">
						<DOI name="Beh">
							<DAI desc="[FE 0E] - EfdPTOC2.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[39 36] - IN2&gt;2 Function" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[39 36] - IN2&gt;2 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 266" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 266" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[39 37] - IN2&gt;2 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[39 37] - IN2&gt;2 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 320" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 320" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="IN2&gt; 3 Earth Fault (Derived)" lnType="PTOC_NEU" lnClass="PTOC" inst="3" prefix="Efd">
						<DOI name="Beh">
							<DAI desc="[FE 0F] - EfdPTOC3.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[39 46] - IN2&gt;3 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[39 46] - IN2&gt;3 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 267" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 267" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[39 47] - IN2&gt;3 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[39 47] - IN2&gt;3 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 321" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 321" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="IN2&gt; 4 Earth Fault (Derived)" lnType="PTOC_NEU" lnClass="PTOC" inst="4" prefix="Efd">
						<DOI name="Beh">
							<DAI desc="[FE 10] - EfdPTOC4.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[39 4D] - IN2&gt;4 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[39 4D] - IN2&gt;4 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 268" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 268" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[39 4E] - IN2&gt;4 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[39 4E] - IN2&gt;4 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 322" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 322" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="IN1&gt; 1 Earth Fault (Measured)" lnType="PTOC_NEU" lnClass="PTOC" inst="1" prefix="Efm">
						<DOI name="Beh">
							<DAI desc="[FE 11] - EfmPTOC1.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[38 25] - IN1&gt;1 Function" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[38 25] - IN1&gt;1 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 261" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 261" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[38 26] - IN1&gt;1 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[38 26] - IN1&gt;1 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 315" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 315" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="IN1&gt; 2 Earth Fault (Measured)" lnType="PTOC_NEU" lnClass="PTOC" inst="2" prefix="Efm">
						<DOI name="Beh">
							<DAI desc="[FE 12] - EfmPTOC2.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[38 36] - IN1&gt;2 Function" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[38 36] - IN1&gt;2 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 262" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 262" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[38 37] - IN1&gt;2 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[38 37] - IN1&gt;2 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 316" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 316" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="IN1&gt; 3 Earth Fault (Measured)" lnType="PTOC_NEU" lnClass="PTOC" inst="3" prefix="Efm">
						<DOI name="Beh">
							<DAI desc="[FE 13] - EfmPTOC3.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[38 46] - IN1&gt;3 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[38 46] - IN1&gt;3 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 263" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 263" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[38 47] - IN1&gt;3 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[38 47] - IN1&gt;3 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 317" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 317" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="IN1&gt; 4 Earth Fault (Measured)" lnType="PTOC_NEU" lnClass="PTOC" inst="4" prefix="Efm">
						<DOI name="Beh">
							<DAI desc="[FE 14] - EfmPTOC4.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[38 4D] - IN1&gt;4 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[38 4D] - IN1&gt;4 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 264" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 264" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[38 4E] - IN1&gt;4 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[38 4E] - IN1&gt;4 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 318" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 318" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="Physical Device Information" lnType="LPHD_STANDARD" lnClass="LPHD" inst="1">
						<DOI name="PhyHealth">
							<DAI desc="Returns the Health of the Physical Device." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="PhyNam">
							<DAI desc="Returns the Hardware Revision of the Physical Device." name="hwRev" valKind="RO"/>
							<DAI desc="[00 05] - Plant Reference" name="location" valKind="RO"/>
							<DAI desc="[00 06] - Model Number" name="model" valKind="RO"/>
							<DAI desc="[00 08] - Serial Number" name="serNum" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Proxy">
							<DAI name="d" valKind="RO">
								<Val>Logical Device is a Proxy</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="PwrUp">
							<DAI name="d" valKind="RO">
								<Val>Px40 Power-Up Detected</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>true</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="I2&gt; 1 Negative Sequence" lnType="PTOC_NO_SEG" lnClass="PTOC" inst="1" prefix="Ngc">
						<DOI name="Beh">
							<DAI desc="[FE 1B] - NgcPTOC1.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[36 10] - I2&gt;1 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[36 10] - I2&gt;1 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 513" name="general" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[36 12] - I2&gt;1 Directional" name="dirGeneral" valKind="RO"/>
							<DAI desc="DDB Ordinal: 509" name="general" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I2&gt; 2 Negative Sequence" lnType="PTOC_NO_SEG" lnClass="PTOC" inst="2" prefix="Ngc">
						<DOI name="Beh">
							<DAI desc="[FE 1C] - NgcPTOC2.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[36 20] - I2&gt;2 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[36 20] - I2&gt;2 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 514" name="general" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[36 22] - I2&gt;2 Directional" name="dirGeneral" valKind="RO"/>
							<DAI desc="DDB Ordinal: 510" name="general" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I2&gt; 3 Negative Sequence" lnType="PTOC_NO_SEG" lnClass="PTOC" inst="3" prefix="Ngc">
						<DOI name="Beh">
							<DAI desc="[FE 1D] - NgcPTOC3.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[36 30] - I2&gt;3 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[36 30] - I2&gt;3 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 515" name="general" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[36 32] - I2&gt;3 Directional" name="dirGeneral" valKind="RO"/>
							<DAI desc="DDB Ordinal: 511" name="general" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I2&gt; 4 Negative Sequence" lnType="PTOC_NO_SEG" lnClass="PTOC" inst="4" prefix="Ngc">
						<DOI name="Beh">
							<DAI desc="[FE 1E] - NgcPTOC4.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[36 40] - I2&gt;4 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[36 40] - I2&gt;4 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 516" name="general" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[36 42] - I2&gt;4 Directional" name="dirGeneral" valKind="RO"/>
							<DAI desc="DDB Ordinal: 512" name="general" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I&gt; 1 Overcurrent" lnType="PTOC_SEG" lnClass="PTOC" inst="1" prefix="Ocp">
						<DOI name="Beh">
							<DAI desc="[FE 01] - OcpPTOC1.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[35 23] - I&gt;1 Function" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[35 23] - I&gt;1 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 243" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 244" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 245" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 246" name="phsC" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[35 24] - I&gt;1 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[35 24] - I&gt;1 Direction" name="dirPhsA" valKind="RO"/>
							<DAI desc="[35 24] - I&gt;1 Direction" name="dirPhsB" valKind="RO"/>
							<DAI desc="[35 24] - I&gt;1 Direction" name="dirPhsC" valKind="RO"/>
							<DAI desc="DDB Ordinal: 295" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 296" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 297" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 298" name="phsC" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I&gt; 2 Overcurrent" lnType="PTOC_SEG" lnClass="PTOC" inst="2" prefix="Ocp">
						<DOI name="Beh">
							<DAI desc="[FE 02] - OcpPTOC2.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[35 32] - I&gt;2 Function" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[35 32] - I&gt;2 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 247" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 248" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 249" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 250" name="phsC" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[35 33] - I&gt;2 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[35 33] - I&gt;2 Direction" name="dirPhsA" valKind="RO"/>
							<DAI desc="[35 33] - I&gt;2 Direction" name="dirPhsB" valKind="RO"/>
							<DAI desc="[35 33] - I&gt;2 Direction" name="dirPhsC" valKind="RO"/>
							<DAI desc="DDB Ordinal: 299" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 300" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 301" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 302" name="phsC" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I&gt; 3 Overcurrent" lnType="PTOC_SEG" lnClass="PTOC" inst="3" prefix="Ocp">
						<DOI name="Beh">
							<DAI desc="[FE 03] - OcpPTOC3.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[35 40] - I&gt;3 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[35 40] - I&gt;3 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 251" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 252" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 253" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 254" name="phsC" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[35 41] - I&gt;3 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[35 41] - I&gt;3 Direction" name="dirPhsA" valKind="RO"/>
							<DAI desc="[35 41] - I&gt;3 Direction" name="dirPhsB" valKind="RO"/>
							<DAI desc="[35 41] - I&gt;3 Direction" name="dirPhsC" valKind="RO"/>
							<DAI desc="DDB Ordinal: 303" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 304" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 305" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 306" name="phsC" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I&gt; 4 Overcurrent" lnType="PTOC_SEG" lnClass="PTOC" inst="4" prefix="Ocp">
						<DOI name="Beh">
							<DAI desc="[FE 04] - OcpPTOC4.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[35 47] - I&gt;4 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[35 47] - I&gt;4 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 255" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 256" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 257" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 258" name="phsC" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[35 48] - I&gt;4 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[35 48] - I&gt;4 Direction" name="dirPhsA" valKind="RO"/>
							<DAI desc="[35 48] - I&gt;4 Direction" name="dirPhsB" valKind="RO"/>
							<DAI desc="[35 48] - I&gt;4 Direction" name="dirPhsC" valKind="RO"/>
							<DAI desc="DDB Ordinal: 307" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 308" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 309" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 310" name="phsC" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I&gt; 5 Overcurrent" lnType="PTOC_SEG" lnClass="PTOC" inst="5" prefix="Ocp">
						<DOI name="Beh">
							<DAI desc="[FE 39] - OcpPTOC5.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[35 63] - I&gt;5 Function" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[35 63] - I&gt;5 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 570" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 571" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 572" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 573" name="phsC" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[35 64] - I&gt;5 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[35 64] - I&gt;5 Direction" name="dirPhsA" valKind="RO"/>
							<DAI desc="[35 64] - I&gt;5 Direction" name="dirPhsB" valKind="RO"/>
							<DAI desc="[35 64] - I&gt;5 Direction" name="dirPhsC" valKind="RO"/>
							<DAI desc="DDB Ordinal: 579" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 580" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 581" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 582" name="phsC" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="I&gt; 6 Overcurrent" lnType="PTOC_SEG" lnClass="PTOC" inst="6" prefix="Ocp">
						<DOI name="Beh">
							<DAI desc="[FE 3A] - OcpPTOC6.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[35 71] - I&gt;6 Status" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[35 71] - I&gt;6 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 574" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 575" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 576" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 577" name="phsC" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[35 72] - I&gt;6 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[35 72] - I&gt;6 Direction" name="dirPhsA" valKind="RO"/>
							<DAI desc="[35 72] - I&gt;6 Direction" name="dirPhsB" valKind="RO"/>
							<DAI desc="[35 72] - I&gt;6 Direction" name="dirPhsC" valKind="RO"/>
							<DAI desc="DDB Ordinal: 583" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 584" name="phsA" valKind="RO"/>
							<DAI desc="DDB Ordinal: 585" name="phsB" valKind="RO"/>
							<DAI desc="DDB Ordinal: 586" name="phsC" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="Protection Trip Conditioning" lnType="PTRC_NO_SEG" lnClass="PTRC" inst="1">
						<DOI name="Beh">
							<DAI desc="[0F 0D] - Test Mode" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[0F 0D] - Test Mode" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Protection Trip Conditioning</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Str">
							<DAI name="dirGeneral" valKind="RO">
								<Val>unknown</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 294" name="general" valKind="RO"/>
						</DOI>
						<DOI name="Tr">
							<DAI desc="DDB Ordinal: 536" name="general" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="ISEF&gt; 1 Sensitive Earth Fault" lnType="PTOC_NEU" lnClass="PTOC" inst="1" prefix="SenEft">
						<DOI name="Beh">
							<DAI desc="[FE 2B] - SenSefPTOC1.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[FE 27] - SenSefPTOC1.ST.Mod.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[3A 2A] - ISEF&gt;1 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 269" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 269" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[3A 2B] - ISEF&gt;1 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[3A 2B] - ISEF&gt;1 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 323" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 323" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="ISEF&gt; 2 Sensitive Earth Fault" lnType="PTOC_NEU" lnClass="PTOC" inst="2" prefix="SenEft">
						<DOI name="Beh">
							<DAI desc="[FE 2C] - SenSefPTOC2.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[FE 28] - SenSefPTOC2.ST.Mod.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[3A 3A] - ISEF&gt;2 Function" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 270" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 270" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[3A 3B] - ISEF&gt;2 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[3A 3B] - ISEF&gt;2 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 324" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 324" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="ISEF&gt; 3 Sensitive Earth Fault" lnType="PTOC_NEU" lnClass="PTOC" inst="3" prefix="SenEft">
						<DOI name="Beh">
							<DAI desc="[FE 2D] - SenSefPTOC3.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[FE 29] - SenSefPTOC3.ST.Mod.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[3A 49] - ISEF&gt;3 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 271" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 271" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[3A 4A] - ISEF&gt;3 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[3A 4A] - ISEF&gt;3 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 325" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 325" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="ISEF&gt; 4 Sensitive Earth Fault" lnType="PTOC_NEU" lnClass="PTOC" inst="4" prefix="SenEft">
						<DOI name="Beh">
							<DAI desc="[FE 2E] - SenSefPTOC4.ST.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[FE 2A] - SenSefPTOC4.ST.Mod.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[3A 50] - ISEF&gt;4 Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 272" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 272" name="neut" valKind="RO"/>
						</DOI>
						<DOI name="Str">
							<DAI desc="[3A 51] - ISEF&gt;4 Direction" name="dirGeneral" valKind="RO"/>
							<DAI desc="[3A 51] - ISEF&gt;4 Direction" name="dirNeut" valKind="RO"/>
							<DAI desc="DDB Ordinal: 326" name="general" valKind="RO"/>
							<DAI desc="DDB Ordinal: 326" name="neut" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="Thermal Overload" lnType="PTTR_NO_SEG" lnClass="PTTR" inst="1" prefix="Thm">
						<Private type="MiCOM-ConfigurableAttributes">Amp.db,TmpRl.db,Amp.rangeC.min.f,Amp.rangeC.max.f,TmpRl.rangeC.min.f,TmpRl.rangeC.max.f,Amp.units.multiplier,TmpRl.units.multiplier,MTRRs.ctlModel,MTRRs.sboTimeout,</Private>
						<DOI name="AlmThm">
							<DAI desc="DDB Ordinal: 329" name="general" valKind="RO"/>
						</DOI>
						<DOI name="Amp">
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[04 01] - Highest Phase I" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>3</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[04 01] - Highest Phase I" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Beh">
							<DAI desc="[FE 2F] - ThmPTTR1.Beh.stVal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[3C 01] - Characteristic" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="MTRRs">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[04 03] - Reset Thermal" name="ctlVal" valKind="RO"/>
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="[04 03] - Reset Thermal" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[09 17] - Thermal Overload" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Op">
							<DAI desc="DDB Ordinal: 276" name="general" valKind="RO"/>
						</DOI>
						<DOI name="TmpRl">
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[04 02] - Thermal State" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>100</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[04 02] - Thermal State" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
					</LN>
				</LDevice>
				<LDevice desc="P14NB Records Domain" inst="Records">
					<LN0 desc="Records Logical Device" lnType="LLN0_STANDARD" inst="" lnClass="LLN0">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="Returns the Configuration Revision of the Physical Device." name="configRev" valKind="RO"/>
							<DAI name="d" valKind="RO">
								<Val>Records Logical Device</Val>
							</DAI>
							<DAI name="ldNs" valKind="RO">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN0>
					<LN desc="Physical Device Information" lnType="LPHD_STANDARD" lnClass="LPHD" inst="1">
						<DOI name="PhyHealth">
							<DAI desc="Returns the Health of the Physical Device." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="PhyNam">
							<DAI desc="Returns the Hardware Revision of the Physical Device." name="hwRev" valKind="RO"/>
							<DAI desc="[00 05] - Plant Reference" name="location" valKind="RO"/>
							<DAI desc="[00 06] - Model Number" name="model" valKind="RO"/>
							<DAI desc="[00 08] - Serial Number" name="serNum" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Proxy">
							<DAI name="d" valKind="RO">
								<Val>Logical Device is a Proxy</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="PwrUp">
							<DAI name="d" valKind="RO">
								<Val>Px40 Power-Up Detected</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>true</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Disturbance Recorder" lnType="RDRE_BASIC" lnClass="RDRE" inst="1">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="FltNum">
							<DAI name="stVal" valKind="RO">
								<Val>0</Val>
							</DAI>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Disturbance Recorder Function</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="RcdMade">
							<DAI desc="Returns if a Disturbance Record is available for Extraction." name="stVal" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="Fault Locator" lnType="RFLO_BASIC" lnClass="RFLO" inst="1">
						<Private type="MiCOM-ConfigurableAttributes">FltZ.db,FltDiskm.db,FltZ.rangeC.min.f,FltZ.rangeC.max.f,FltDiskm.rangeC.min.f,FltDiskm.rangeC.max.f,FltZ.units.multiplier,FltDiskm.units.multiplier,</Private>
						<DOI name="Beh">
							<DAI desc="[09 22] - Fault Locator" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="FltDiskm">
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="mag">
								<DAI desc="[01 13] - Fault Location (metres)" name="f" valKind="RO"/>
							</SDI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>1000</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[01 13] - Fault Location (metres)" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="FltZ">
							<SDI name="cVal">
								<SDI name="mag">
									<DAI desc="[01 15] - Fault Location (impedance)" name="f" valKind="RO"/>
								</SDI>
							</SDI>
							<DAI name="db" valKind="Set">
								<Val>1000</Val>
							</DAI>
							<SDI name="rangeC">
								<SDI name="hhLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="hLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="lLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="llLim">
									<DAI name="f" valKind="RO">
										<Val>0</Val>
									</DAI>
								</SDI>
								<SDI name="max">
									<DAI name="f" valKind="Set">
										<Val>1000</Val>
									</DAI>
								</SDI>
								<SDI name="min">
									<DAI name="f" valKind="Set">
										<Val>0</Val>
									</DAI>
								</SDI>
							</SDI>
							<SDI name="units">
								<DAI name="multiplier" valKind="Set">
									<Val/>
								</DAI>
								<DAI desc="[01 15] - Fault Location (impedance)" name="SIUnit" valKind="RO"/>
							</SDI>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[09 22] - Fault Locator" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Fault Locator Function</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
				</LDevice>
				<LDevice desc="P14NB System Domain" inst="System">
					<LN0 desc="System Logical Device" lnType="LLN0_SYSTEM" inst="" lnClass="LLN0">
						<Private type="MiCOM-ConfigurableAttributes">LEDRs.ctlModel,LEDRs.sboTimeout,</Private>
						<DataSet name="dsAlarm">
							<FCDA doName="Alm1" fc="ST" ldInst="System" lnClass="GGIO" lnInst="1" prefix="Alm"/>
						</DataSet>
						<DataSet name="dsOpt">
							<FCDA doName="Ind1" fc="ST" ldInst="System" lnClass="GGIO" lnInst="1" prefix="Opt"/>
						</DataSet>
						<DataSet name="dsTripinfo">
							<FCDA doName="Str" fc="ST" ldInst="Protection" lnClass="PTOC" lnInst="1" prefix="Efd"/>
							<FCDA doName="Op" fc="ST" ldInst="Protection" lnClass="PTOC" lnInst="1" prefix="Efd"/>
							<FCDA doName="Str" fc="ST" ldInst="Protection" lnClass="PTOC" lnInst="2" prefix="Efd"/>
							<FCDA doName="Op" fc="ST" ldInst="Protection" lnClass="PTOC" lnInst="2" prefix="Efd"/>
							<FCDA doName="Str" fc="ST" ldInst="Protection" lnClass="PTOC" lnInst="1" prefix="Efm"/>
							<FCDA doName="Op" fc="ST" ldInst="Protection" lnClass="PTOC" lnInst="1" prefix="Efm"/>
							<FCDA doName="Str" fc="ST" ldInst="Protection" lnClass="PTOC" lnInst="2" prefix="Efm"/>
							<FCDA doName="Op" fc="ST" ldInst="Protection" lnClass="PTOC" lnInst="2" prefix="Efm"/>
						</DataSet>
						<DataSet name="dsAin">
							<FCDA doName="A" fc="MX" ldInst="Measurements" lnClass="MMXU" lnInst="1" prefix="PriRms"/>
							<FCDA doName="A" fc="MX" ldInst="Measurements" lnClass="MMXU" lnInst="1" prefix="SecRms"/>
						</DataSet>
						<ReportControl name="urcbA" desc="System Logical Device Report Control Block" datSet="dsAlarm" rptID="P14NBSystem/LLN0$RP$urcbA01" confRev="3" buffered="false">
							<TrgOps dchg="true" qchg="false" dupd="false" period="true"/>
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="false" configRef="false"/>
							<RptEnabled max="1"/>
						</ReportControl>
						<ReportControl name="urcbB" desc="System Logical Device Report Control Block" datSet="dsOpt" rptID="P14NBSystem/LLN0$RP$urcbB01" confRev="3" buffered="false">
							<TrgOps dchg="true" qchg="false" dupd="false" period="true"/>
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="false" configRef="false"/>
							<RptEnabled max="1"/>
						</ReportControl>
						<ReportControl name="urcbC" desc="System Logical Device Report Control Block" datSet="dsTripinfo" rptID="P14NBSystem/LLN0$RP$urcbC01" confRev="3" buffered="false">
							<TrgOps dchg="true" qchg="false" dupd="false" period="true"/>
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="false" configRef="false"/>
							<RptEnabled max="1"/>
						</ReportControl>
						<ReportControl name="urcbD" desc="System Logical Device Report Control Block" datSet="dsAin" rptID="P14NBSystem/LLN0$RP$urcbD01" confRev="3" buffered="false">
							<TrgOps dchg="true" qchg="false" dupd="false" period="true"/>
							<OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="false" entryID="false" configRef="false"/>
							<RptEnabled max="1"/>
						</ReportControl>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="LEDRs">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[01 FF] - Reset Indication" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="[01 FF] - Reset Indication" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="Returns the Configuration Revision of the Physical Device." name="configRev" valKind="RO"/>
							<DAI name="d" valKind="RO">
								<Val>System Logical Device</Val>
							</DAI>
							<DAI name="ldNs" valKind="RO">
								<Val>IEC 61850-7-4:2003</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="OrdRun">
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="Order Running indication" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SyncSt">
							<DAI name="dataNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI desc="IED time synchronisation state" name="stVal" valKind="RO"/>
						</DOI>
						<SettingControl numOfSGs="4" actSG="1"/>
					</LN0>
					<LN desc="Alarms" lnType="GGIO_ALM_96" lnClass="GGIO" inst="1" prefix="Alm">
						<DOI name="Alm1">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm10">
							<DAI desc="DDB Ordinal: 152" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 152" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm11">
							<DAI desc="DDB Ordinal: 153" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 153" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm12">
							<DAI desc="DDB Ordinal: 154" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 154" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm13">
							<DAI desc="DDB Ordinal: 155" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 155" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm14">
							<DAI desc="DDB Ordinal: 156" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 156" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm15">
							<DAI desc="DDB Ordinal: 157" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 157" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm16">
							<DAI desc="DDB Ordinal: 158" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 158" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm17">
							<DAI desc="DDB Ordinal: 159" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 159" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm18">
							<DAI desc="DDB Ordinal: 160" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 160" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm19">
							<DAI desc="DDB Ordinal: 161" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 161" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm2">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm20">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm21">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm22">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm23">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm24">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm25">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm26">
							<DAI desc="DDB Ordinal: 168" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 168" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm27">
							<DAI desc="DDB Ordinal: 169" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 169" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm28">
							<DAI desc="DDB Ordinal: 170" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 170" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm29">
							<DAI desc="DDB Ordinal: 171" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 171" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm3">
							<DAI desc="DDB Ordinal: 145" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 145" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm30">
							<DAI desc="DDB Ordinal: 172" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 172" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm31">
							<DAI desc="DDB Ordinal: 173" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 173" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm32">
							<DAI desc="DDB Ordinal: 174" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 174" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm33">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm34">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm35">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm36">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm37">
							<DAI desc="DDB Ordinal: 175" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 175" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm38">
							<DAI desc="DDB Ordinal: 176" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 176" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm39">
							<DAI desc="DDB Ordinal: 177" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 177" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm4">
							<DAI desc="DDB Ordinal: 146" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 146" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm40">
							<DAI desc="DDB Ordinal: 178" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 178" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm41">
							<DAI desc="DDB Ordinal: 179" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 179" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm42">
							<DAI desc="DDB Ordinal: 180" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 180" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm43">
							<DAI desc="DDB Ordinal: 181" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 181" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm44">
							<DAI desc="DDB Ordinal: 182" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 182" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm45">
							<DAI desc="DDB Ordinal: 183" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 183" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm46">
							<DAI desc="DDB Ordinal: 184" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 184" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm47">
							<DAI desc="DDB Ordinal: 185" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 185" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm48">
							<DAI desc="DDB Ordinal: 186" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 186" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm49">
							<DAI desc="DDB Ordinal: 187" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 187" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm5">
							<DAI desc="DDB Ordinal: 147" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 147" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm50">
							<DAI desc="DDB Ordinal: 188" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 188" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm51">
							<DAI desc="DDB Ordinal: 189" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 189" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm52">
							<DAI desc="DDB Ordinal: 190" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 190" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm53">
							<DAI desc="DDB Ordinal: 191" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 191" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm54">
							<DAI desc="DDB Ordinal: 192" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 192" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm55">
							<DAI desc="DDB Ordinal: 193" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 193" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm56">
							<DAI desc="DDB Ordinal: 194" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 194" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm57">
							<DAI desc="DDB Ordinal: 195" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 195" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm58">
							<DAI desc="DDB Ordinal: 196" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 196" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm59">
							<DAI desc="DDB Ordinal: 197" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 197" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm6">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm60">
							<DAI desc="DDB Ordinal: 198" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 198" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm61">
							<DAI desc="DDB Ordinal: 199" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 199" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm62">
							<DAI desc="DDB Ordinal: 200" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 200" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm63">
							<DAI desc="DDB Ordinal: 201" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 201" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm64">
							<DAI desc="DDB Ordinal: 202" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 202" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm65">
							<DAI desc="DDB Ordinal: 769" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 769" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm66">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm67">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm68">
							<DAI desc="DDB Ordinal: 771" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 771" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm69">
							<DAI desc="DDB Ordinal: 772" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 772" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm7">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm70">
							<DAI desc="DDB Ordinal: 773" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 773" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm71">
							<DAI desc="DDB Ordinal: 774" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 774" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm72">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm73">
							<DAI desc="DDB Ordinal: 776" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 776" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm74">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm75">
							<DAI desc="DDB Ordinal: 778" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 778" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm76">
							<DAI desc="DDB Ordinal: 779" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 779" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm77">
							<DAI desc="DDB Ordinal: 780" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 780" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm78">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm79">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm8">
							<DAI desc="DDB Ordinal: 150" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 150" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm80">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm81">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm82">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm83">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm84">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm85">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm86">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm87">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm88">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm89">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm9">
							<DAI desc="DDB Ordinal: 151" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 151" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Alm90">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm91">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm92">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm93">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm94">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm95">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Alm96">
							<DAI name="d" valKind="RO">
								<Val>Unused</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Alarm Status</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Function Keys" lnType="GGIO_IND_3" lnClass="GGIO" inst="1" prefix="Fnk">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Ind1">
							<DAI desc="[17 04] - Fn Key 1 Label" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 712" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind2">
							<DAI desc="[17 07] - Fn Key 2 Label" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 713" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind3">
							<DAI desc="[17 0A] - Fn Key 3 Label" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 714" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[17 01] - Fn Key Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="GOOSE Input Signals" lnType="GGIO_IND_32" lnClass="GGIO" inst="1" prefix="Gos">
						<Private type="MiCOM-BindableType">VI</Private>
						<Private type="MiCOM-BindableSignals">
									Ind1.stVal,
									Ind2.stVal,
									Ind3.stVal,
									Ind4.stVal,
									Ind5.stVal,
									Ind6.stVal,
									Ind7.stVal,
									Ind8.stVal,
									Ind9.stVal,
									Ind10.stVal,
									Ind11.stVal,
									Ind12.stVal,
									Ind13.stVal,
									Ind14.stVal,
									Ind15.stVal,
									Ind16.stVal,
									Ind17.stVal,
									Ind18.stVal,
									Ind19.stVal,
									Ind20.stVal,
									Ind21.stVal,
									Ind22.stVal,
									Ind23.stVal,
									Ind24.stVal,
									Ind25.stVal,
									Ind26.stVal,
									Ind27.stVal,
									Ind28.stVal,
									Ind29.stVal,
									Ind30.stVal,
									Ind31.stVal,
									Ind32.stVal,
						</Private>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Ind1">
							<DAI name="d" valKind="RO">
								<Val>VIP 1</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 832" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind10">
							<DAI name="d" valKind="RO">
								<Val>VIP 10</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 841" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind11">
							<DAI name="d" valKind="RO">
								<Val>VIP 11</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 842" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind12">
							<DAI name="d" valKind="RO">
								<Val>VIP 12</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 843" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind13">
							<DAI name="d" valKind="RO">
								<Val>VIP 13</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 844" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind14">
							<DAI name="d" valKind="RO">
								<Val>VIP 14</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 845" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind15">
							<DAI name="d" valKind="RO">
								<Val>VIP 15</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 846" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind16">
							<DAI name="d" valKind="RO">
								<Val>VIP 16</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 847" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind17">
							<DAI name="d" valKind="RO">
								<Val>VIP 17</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 848" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind18">
							<DAI name="d" valKind="RO">
								<Val>VIP 18</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 849" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind19">
							<DAI name="d" valKind="RO">
								<Val>VIP 19</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 850" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind2">
							<DAI name="d" valKind="RO">
								<Val>VIP 2</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 833" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind20">
							<DAI name="d" valKind="RO">
								<Val>VIP 20</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 851" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind21">
							<DAI name="d" valKind="RO">
								<Val>VIP 21</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 852" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind22">
							<DAI name="d" valKind="RO">
								<Val>VIP 22</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 853" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind23">
							<DAI name="d" valKind="RO">
								<Val>VIP 23</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 854" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind24">
							<DAI name="d" valKind="RO">
								<Val>VIP 24</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 855" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind25">
							<DAI name="d" valKind="RO">
								<Val>VIP 25</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 856" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind26">
							<DAI name="d" valKind="RO">
								<Val>VIP 26</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 857" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind27">
							<DAI name="d" valKind="RO">
								<Val>VIP 27</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 858" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind28">
							<DAI name="d" valKind="RO">
								<Val>VIP 28</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 859" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind29">
							<DAI name="d" valKind="RO">
								<Val>VIP 29</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 860" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind3">
							<DAI name="d" valKind="RO">
								<Val>VIP 3</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 834" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind30">
							<DAI name="d" valKind="RO">
								<Val>VIP 30</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 861" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind31">
							<DAI name="d" valKind="RO">
								<Val>VIP 31</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 862" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind32">
							<DAI name="d" valKind="RO">
								<Val>VIP 32</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 863" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind4">
							<DAI name="d" valKind="RO">
								<Val>VIP 4</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 835" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind5">
							<DAI name="d" valKind="RO">
								<Val>VIP 5</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 836" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind6">
							<DAI name="d" valKind="RO">
								<Val>VIP 6</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 837" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind7">
							<DAI name="d" valKind="RO">
								<Val>VIP 7</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 838" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind8">
							<DAI name="d" valKind="RO">
								<Val>VIP 8</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 839" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind9">
							<DAI name="d" valKind="RO">
								<Val>VIP 9</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 840" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>GOOSE Input Status</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="GOOSE Output Signals" lnType="GGIO_IND_32" lnClass="GGIO" inst="2" prefix="Gos">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Ind1">
							<DAI desc="DDB Ordinal: 1024" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1024" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind10">
							<DAI desc="DDB Ordinal: 1033" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1033" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind11">
							<DAI desc="DDB Ordinal: 1034" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1034" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind12">
							<DAI desc="DDB Ordinal: 1035" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1035" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind13">
							<DAI desc="DDB Ordinal: 1036" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1036" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind14">
							<DAI desc="DDB Ordinal: 1037" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1037" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind15">
							<DAI desc="DDB Ordinal: 1038" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1038" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind16">
							<DAI desc="DDB Ordinal: 1039" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1039" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind17">
							<DAI desc="DDB Ordinal: 1040" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1040" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind18">
							<DAI desc="DDB Ordinal: 1041" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1041" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind19">
							<DAI desc="DDB Ordinal: 1042" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1042" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind2">
							<DAI desc="DDB Ordinal: 1025" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1025" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind20">
							<DAI desc="DDB Ordinal: 1043" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1043" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind21">
							<DAI desc="DDB Ordinal: 1044" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1044" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind22">
							<DAI desc="DDB Ordinal: 1045" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1045" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind23">
							<DAI desc="DDB Ordinal: 1046" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1046" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind24">
							<DAI desc="DDB Ordinal: 1047" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1047" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind25">
							<DAI desc="DDB Ordinal: 1048" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1048" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind26">
							<DAI desc="DDB Ordinal: 1049" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1049" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind27">
							<DAI desc="DDB Ordinal: 1050" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1050" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind28">
							<DAI desc="DDB Ordinal: 1051" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1051" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind29">
							<DAI desc="DDB Ordinal: 1052" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1052" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind3">
							<DAI desc="DDB Ordinal: 1026" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1026" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind30">
							<DAI desc="DDB Ordinal: 1053" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1053" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind31">
							<DAI desc="DDB Ordinal: 1054" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1054" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind32">
							<DAI desc="DDB Ordinal: 1055" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1055" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind4">
							<DAI desc="DDB Ordinal: 1027" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1027" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind5">
							<DAI desc="DDB Ordinal: 1028" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1028" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind6">
							<DAI desc="DDB Ordinal: 1029" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1029" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind7">
							<DAI desc="DDB Ordinal: 1030" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1030" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind8">
							<DAI desc="DDB Ordinal: 1031" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1031" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind9">
							<DAI desc="DDB Ordinal: 1032" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1032" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>GOOSE Output Status</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Red LED Signals" lnType="GGIO_IND_11" lnClass="GGIO" inst="1" prefix="Led">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Ind1">
							<DAI desc="DDB Ordinal: 640" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 640" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind10">
							<DAI desc="DDB Ordinal: 658" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 658" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind11">
							<DAI desc="DDB Ordinal: 660" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 660" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind2">
							<DAI desc="DDB Ordinal: 642" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 642" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind3">
							<DAI desc="DDB Ordinal: 644" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 644" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind4">
							<DAI desc="DDB Ordinal: 646" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 646" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind5">
							<DAI desc="DDB Ordinal: 648" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 648" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind6">
							<DAI desc="DDB Ordinal: 650" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 650" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind7">
							<DAI desc="DDB Ordinal: 652" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 652" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind8">
							<DAI desc="DDB Ordinal: 654" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 654" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind9">
							<DAI desc="DDB Ordinal: 656" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 656" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Red LED Signals</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Green LED Signals" lnType="GGIO_IND_11" lnClass="GGIO" inst="2" prefix="Led">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Ind1">
							<DAI desc="DDB Ordinal: 641" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 641" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind10">
							<DAI desc="DDB Ordinal: 659" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 659" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind11">
							<DAI desc="DDB Ordinal: 661" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 661" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind2">
							<DAI desc="DDB Ordinal: 643" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 643" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind3">
							<DAI desc="DDB Ordinal: 645" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 645" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind4">
							<DAI desc="DDB Ordinal: 647" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 647" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind5">
							<DAI desc="DDB Ordinal: 649" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 649" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind6">
							<DAI desc="DDB Ordinal: 651" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 651" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind7">
							<DAI desc="DDB Ordinal: 653" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 653" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind8">
							<DAI desc="DDB Ordinal: 655" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 655" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind9">
							<DAI desc="DDB Ordinal: 657" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 657" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>Green LED Signals</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Physical Device Information" lnType="LPHD_STANDARD" lnClass="LPHD" inst="1">
						<DOI name="PhyHealth">
							<DAI desc="Returns the Health of the Physical Device." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="PhyNam">
							<DAI desc="Returns the Hardware Revision of the Physical Device." name="hwRev" valKind="RO"/>
							<DAI desc="[00 05] - Plant Reference" name="location" valKind="RO"/>
							<DAI desc="[00 06] - Model Number" name="model" valKind="RO"/>
							<DAI desc="[00 08] - Serial Number" name="serNum" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="Proxy">
							<DAI name="d" valKind="RO">
								<Val>Logical Device is a Proxy</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="PwrUp">
							<DAI name="d" valKind="RO">
								<Val>Px40 Power-Up Detected</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>true</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="P14x Opto Input" lnType="GGIO_IND_13" lnClass="GGIO" inst="1" prefix="Opt">
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Ind1">
							<DAI desc="[4A 01] - Opto Input 1" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 32" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind10">
							<DAI desc="[4A 0A] - Opto Input 10" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 41" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind11">
							<DAI desc="[4A 0B] - Opto Input 11" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 42" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind12">
							<DAI desc="[4A 0C] - Opto Input 12" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 43" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind13">
							<DAI desc="[4A 0D] - Opto Input 13" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 44" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind2">
							<DAI desc="[4A 02] - Opto Input 2" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 33" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind3">
							<DAI desc="[4A 03] - Opto Input 3" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 34" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind4">
							<DAI desc="[4A 04] - Opto Input 4" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 35" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind5">
							<DAI desc="[4A 05] - Opto Input 5" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 36" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind6">
							<DAI desc="[4A 06] - Opto Input 6" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 37" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind7">
							<DAI desc="[4A 07] - Opto Input 7" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 38" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind8">
							<DAI desc="[4A 08] - Opto Input 8" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 39" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind9">
							<DAI desc="[4A 09] - Opto Input 9" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 40" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[0F 01] - Opto I/P Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Uniqueness of control &quot;Order Running&quot; indications for Control operations" lnType="GGIO_IND_64" lnClass="GGIO" inst="1" prefix="OrdRun">
						<Private type="MiCOM-BindableType">OrdRun</Private>
						<Private type="MiCOM-BindableSignals">
									Ind1.stVal,
									Ind2.stVal,
									Ind3.stVal,
									Ind4.stVal,
									Ind5.stVal,
									Ind6.stVal,
									Ind7.stVal,
									Ind8.stVal,
									Ind9.stVal,
									Ind10.stVal,
									Ind11.stVal,
									Ind12.stVal,
									Ind13.stVal,
									Ind14.stVal,
									Ind15.stVal,
									Ind16.stVal,
									Ind17.stVal,
									Ind18.stVal,
									Ind19.stVal,
									Ind20.stVal,
									Ind21.stVal,
									Ind22.stVal,
									Ind23.stVal,
									Ind24.stVal,
									Ind25.stVal,
									Ind26.stVal,
									Ind27.stVal,
									Ind28.stVal,
									Ind29.stVal,
									Ind30.stVal,
									Ind31.stVal,
									Ind32.stVal,
									Ind33.stVal,
									Ind34.stVal,
									Ind35.stVal,
									Ind36.stVal,
									Ind37.stVal,
									Ind38.stVal,
									Ind39.stVal,
									Ind40.stVal,
									Ind41.stVal,
									Ind42.stVal,
									Ind43.stVal,
									Ind44.stVal,
									Ind45.stVal,
									Ind46.stVal,
									Ind47.stVal,
									Ind48.stVal,
									Ind49.stVal,
									Ind50.stVal,
									Ind51.stVal,
									Ind52.stVal,
									Ind53.stVal,
									Ind54.stVal,
									Ind55.stVal,
									Ind56.stVal,
									Ind57.stVal,
									Ind58.stVal,
									Ind59.stVal,
									Ind60.stVal,
									Ind61.stVal,
									Ind62.stVal,
									Ind63.stVal,
									Ind64.stVal,
						</Private>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Ind1">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind10">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind11">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind12">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind13">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind14">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind15">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind16">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind17">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind18">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind19">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind2">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind20">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind21">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind22">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind23">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind24">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind25">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind26">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind27">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind28">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind29">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind3">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind30">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind31">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind32">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind33">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind34">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind35">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind36">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind37">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind38">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind39">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind4">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind40">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind41">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind42">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind43">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind44">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind45">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind46">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind47">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind48">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind49">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind5">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind50">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind51">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind52">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind53">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind54">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind55">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind56">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind57">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind58">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind59">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind6">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind60">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind61">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind62">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind63">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind64">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind7">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind8">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Ind9">
							<DAI name="d" valKind="RO">
								<Val>External device order running indication</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>false</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI name="d" valKind="RO">
								<Val>GOOSE Input Status</Val>
							</DAI>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
					<LN desc="Controllable Inputs" lnType="GGIO_IND_64_CTRL" lnClass="GGIO" inst="1" prefix="Plo">
						<Private type="MiCOM-ConfigurableAttributes">SPCSO1.ctlModel,SPCSO1.sboTimeout,SPCSO2.ctlModel,SPCSO2.sboTimeout,SPCSO3.ctlModel,SPCSO3.sboTimeout,SPCSO4.ctlModel,SPCSO4.sboTimeout,SPCSO5.ctlModel,SPCSO5.sboTimeout,SPCSO6.ctlModel,SPCSO6.sboTimeout,SPCSO7.ctlModel,SPCSO7.sboTimeout,SPCSO8.ctlModel,SPCSO8.sboTimeout,SPCSO9.ctlModel,SPCSO9.sboTimeout,SPCSO10.ctlModel,SPCSO10.sboTimeout,SPCSO11.ctlModel,SPCSO11.sboTimeout,SPCSO12.ctlModel,SPCSO12.sboTimeout,SPCSO13.ctlModel,SPCSO13.sboTimeout,SPCSO14.ctlModel,SPCSO14.sboTimeout,SPCSO15.ctlModel,SPCSO15.sboTimeout,SPCSO16.ctlModel,SPCSO16.sboTimeout,SPCSO17.ctlModel,SPCSO17.sboTimeout,SPCSO18.ctlModel,SPCSO18.sboTimeout,SPCSO19.ctlModel,SPCSO19.sboTimeout,SPCSO20.ctlModel,SPCSO20.sboTimeout,SPCSO21.ctlModel,SPCSO21.sboTimeout,SPCSO22.ctlModel,SPCSO22.sboTimeout,SPCSO23.ctlModel,SPCSO23.sboTimeout,SPCSO24.ctlModel,SPCSO24.sboTimeout,SPCSO25.ctlModel,SPCSO25.sboTimeout,SPCSO26.ctlModel,SPCSO26.sboTimeout,SPCSO27.ctlModel,SPCSO27.sboTimeout,SPCSO28.ctlModel,SPCSO28.sboTimeout,SPCSO29.ctlModel,SPCSO29.sboTimeout,SPCSO30.ctlModel,SPCSO30.sboTimeout,SPCSO31.ctlModel,SPCSO31.sboTimeout,SPCSO32.ctlModel,SPCSO32.sboTimeout,SPCSO33.ctlModel,SPCSO33.sboTimeout,SPCSO34.ctlModel,SPCSO34.sboTimeout,SPCSO35.ctlModel,SPCSO35.sboTimeout,SPCSO36.ctlModel,SPCSO36.sboTimeout,SPCSO37.ctlModel,SPCSO37.sboTimeout,SPCSO38.ctlModel,SPCSO38.sboTimeout,SPCSO39.ctlModel,SPCSO39.sboTimeout,SPCSO40.ctlModel,SPCSO40.sboTimeout,SPCSO41.ctlModel,SPCSO41.sboTimeout,SPCSO42.ctlModel,SPCSO42.sboTimeout,SPCSO43.ctlModel,SPCSO43.sboTimeout,SPCSO44.ctlModel,SPCSO44.sboTimeout,SPCSO45.ctlModel,SPCSO45.sboTimeout,SPCSO46.ctlModel,SPCSO46.sboTimeout,SPCSO47.ctlModel,SPCSO47.sboTimeout,SPCSO48.ctlModel,SPCSO48.sboTimeout,SPCSO49.ctlModel,SPCSO49.sboTimeout,SPCSO50.ctlModel,SPCSO50.sboTimeout,SPCSO51.ctlModel,SPCSO51.sboTimeout,SPCSO52.ctlModel,SPCSO52.sboTimeout,SPCSO53.ctlModel,SPCSO53.sboTimeout,SPCSO54.ctlModel,SPCSO54.sboTimeout,SPCSO55.ctlModel,SPCSO55.sboTimeout,SPCSO56.ctlModel,SPCSO56.sboTimeout,SPCSO57.ctlModel,SPCSO57.sboTimeout,SPCSO58.ctlModel,SPCSO58.sboTimeout,SPCSO59.ctlModel,SPCSO59.sboTimeout,SPCSO60.ctlModel,SPCSO60.sboTimeout,SPCSO61.ctlModel,SPCSO61.sboTimeout,SPCSO62.ctlModel,SPCSO62.sboTimeout,SPCSO63.ctlModel,SPCSO63.sboTimeout,SPCSO64.ctlModel,SPCSO64.sboTimeout,</Private>
						<DOI name="Beh">
							<DAI desc="Determines the Mode/Behaviour of the Device based on the 'Test Mode' DDB." name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI name="stVal" valKind="RO">
								<Val>on</Val>
							</DAI>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[12 01] - Ctrl I/P Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
						<DOI name="SPCSO1">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 10] - Control Input 1" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 800" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO10">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 19] - Control Input 10" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 809" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO11">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 1A] - Control Input 11" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 810" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO12">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 1B] - Control Input 12" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 811" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO13">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 1C] - Control Input 13" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 812" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO14">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 1D] - Control Input 14" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 813" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO15">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 1E] - Control Input 15" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 814" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO16">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 1F] - Control Input 16" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 815" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO17">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 20] - Control Input 17" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 816" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO18">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 21] - Control Input 18" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 817" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO19">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 22] - Control Input 19" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 818" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO2">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 11] - Control Input 2" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 801" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO20">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 23] - Control Input 20" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 819" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO21">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 24] - Control Input 21" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 820" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO22">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 25] - Control Input 22" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 821" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO23">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 26] - Control Input 23" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 822" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO24">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 27] - Control Input 24" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 823" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO25">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 28] - Control Input 25" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 824" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO26">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 29] - Control Input 26" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 825" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO27">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 2A] - Control Input 27" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 826" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO28">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 2B] - Control Input 28" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 827" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO29">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 2C] - Control Input 29" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 828" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO3">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 12] - Control Input 3" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 802" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO30">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 2D] - Control Input 30" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 829" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO31">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 2E] - Control Input 31" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 830" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO32">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 2F] - Control Input 32" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 831" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO33">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 30] - Control Input 33" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1248" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO34">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 31] - Control Input 34" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1249" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO35">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 32] - Control Input 35" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1250" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO36">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 33] - Control Input 36" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1251" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO37">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 34] - Control Input 37" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1252" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO38">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 35] - Control Input 38" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1253" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO39">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 36] - Control Input 39" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1254" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO4">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 13] - Control Input 4" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 803" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO40">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 37] - Control Input 40" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1255" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO41">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 38] - Control Input 41" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1256" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO42">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 39] - Control Input 42" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1257" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO43">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 3A] - Control Input 43" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1258" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO44">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 3B] - Control Input 44" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1259" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO45">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 3C] - Control Input 45" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1260" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO46">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 3D] - Control Input 46" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1261" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO47">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 3E] - Control Input 47" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1262" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO48">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 3F] - Control Input 48" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1263" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO49">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 40] - Control Input 49" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1264" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO5">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 14] - Control Input 5" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 804" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO50">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 41] - Control Input 50" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1265" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO51">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 42] - Control Input 51" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1266" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO52">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 43] - Control Input 52" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1267" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO53">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 44] - Control Input 53" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1268" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO54">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 45] - Control Input 54" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1269" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO55">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 46] - Control Input 55" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1270" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO56">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 47] - Control Input 56" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1271" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO57">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 48] - Control Input 57" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1272" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO58">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 49] - Control Input 58" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1273" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO59">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 4A] - Control Input 59" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1274" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO6">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 15] - Control Input 6" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 805" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO60">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 4B] - Control Input 60" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1275" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO61">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 4C] - Control Input 61" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1276" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO62">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 4D] - Control Input 62" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1277" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO63">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 4E] - Control Input 63" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1278" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO64">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 4F] - Control Input 64" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 1279" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO7">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 16] - Control Input 7" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 806" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO8">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 17] - Control Input 8" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 807" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="SPCSO9">
							<DAI name="cdcName" valKind="RO">
								<Val>SPC</Val>
							</DAI>
							<DAI name="cdcNs" valKind="RO">
								<Val>ALSTOM-SII:PCS-Px40&gt;IEC 61850-7-3:2003</Val>
							</DAI>
							<DAI name="ctlModel" valKind="Set">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[12 18] - Control Input 9" name="ctlVal" valKind="RO"/>
							<SDI name="origin">
								<DAI name="orCat" valKind="RO">
									<Val>process</Val>
								</DAI>
								<DAI name="orIdent" valKind="RO">
									<Val>Dummy string</Val>
								</DAI>
							</SDI>
							<DAI name="sboTimeout" valKind="Set">
								<Val>30000</Val>
							</DAI>
							<DAI desc="DDB Ordinal: 808" name="stVal" valKind="RO"/>
						</DOI>
					</LN>
					<LN desc="P14x 12 Relay Contacts" lnType="GGIO_IND_12" lnClass="GGIO" inst="1" prefix="Rly">
						<DOI name="Beh">
							<DAI desc="[0F 0D] - Test Mode" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Health">
							<DAI name="stVal" valKind="RO">
								<Val>Ok</Val>
							</DAI>
						</DOI>
						<DOI name="Ind1">
							<DAI desc="[4B 01] - Relay 1" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 0" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind10">
							<DAI desc="[4B 0A] - Relay 10" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 9" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind11">
							<DAI desc="[4B 0B] - Relay 11" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 11" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind12">
							<DAI desc="[4B 0C] - Relay 12" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 11" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind2">
							<DAI desc="[4B 02] - Relay 2" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 1" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind3">
							<DAI desc="[4B 03] - Relay 3" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 2" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind4">
							<DAI desc="[4B 04] - Relay 4" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 3" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind5">
							<DAI desc="[4B 05] - Relay 5" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 4" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind6">
							<DAI desc="[4B 06] - Relay 6" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 5" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind7">
							<DAI desc="[4B 07] - Relay 7" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 6" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind8">
							<DAI desc="[4B 08] - Relay 8" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 7" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Ind9">
							<DAI desc="[4B 09] - Relay 9" name="d" valKind="RO"/>
							<DAI desc="DDB Ordinal: 8" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="Mod">
							<DAI name="ctlModel" valKind="RO">
								<Val>status-only</Val>
							</DAI>
							<DAI desc="[0F 0D] - Test Mode" name="stVal" valKind="RO"/>
						</DOI>
						<DOI name="NamPlt">
							<DAI desc="[0F 02] - Relay O/P Status" name="d" valKind="RO"/>
							<DAI desc="[00 11] - Software Ref. 1" name="swRev" valKind="RO"/>
							<DAI name="vendor" valKind="RO">
								<Val>MiCOM</Val>
							</DAI>
						</DOI>
					</LN>
				</LDevice>
			</Server>
		</AccessPoint>
	</IED>
	<IED name="CLOCK1" desc="CLOCK1">
		<AccessPoint name="CLOCK1" clock="true"/>
	</IED>
	<IED name="CLOCK2" desc="CLOCK2">
		<AccessPoint name="CLOCK2" clock="true"/>
	</IED>
	<DataTypeTemplates>
		<LNodeType id="CILO_INTERLOCK" desc="Control Interlocking" lnClass="CILO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="EnaOpn" type="SPS_WD"/>
			<DO name="EnaCls" type="SPS_WD"/>
		</LNodeType>
		<LNodeType id="GGIO_ALM_96" desc="Generic Process I/O (w.r.t 96 Alarm Elements)" lnClass="GGIO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Alm1" type="SPS_D"/>
			<DO name="Alm2" type="SPS_D"/>
			<DO name="Alm3" type="SPS_D"/>
			<DO name="Alm4" type="SPS_D"/>
			<DO name="Alm5" type="SPS_D"/>
			<DO name="Alm6" type="SPS_D"/>
			<DO name="Alm7" type="SPS_D"/>
			<DO name="Alm8" type="SPS_D"/>
			<DO name="Alm9" type="SPS_D"/>
			<DO name="Alm10" type="SPS_D"/>
			<DO name="Alm11" type="SPS_D"/>
			<DO name="Alm12" type="SPS_D"/>
			<DO name="Alm13" type="SPS_D"/>
			<DO name="Alm14" type="SPS_D"/>
			<DO name="Alm15" type="SPS_D"/>
			<DO name="Alm16" type="SPS_D"/>
			<DO name="Alm17" type="SPS_D"/>
			<DO name="Alm18" type="SPS_D"/>
			<DO name="Alm19" type="SPS_D"/>
			<DO name="Alm20" type="SPS_D"/>
			<DO name="Alm21" type="SPS_D"/>
			<DO name="Alm22" type="SPS_D"/>
			<DO name="Alm23" type="SPS_D"/>
			<DO name="Alm24" type="SPS_D"/>
			<DO name="Alm25" type="SPS_D"/>
			<DO name="Alm26" type="SPS_D"/>
			<DO name="Alm27" type="SPS_D"/>
			<DO name="Alm28" type="SPS_D"/>
			<DO name="Alm29" type="SPS_D"/>
			<DO name="Alm30" type="SPS_D"/>
			<DO name="Alm31" type="SPS_D"/>
			<DO name="Alm32" type="SPS_D"/>
			<DO name="Alm33" type="SPS_D"/>
			<DO name="Alm34" type="SPS_D"/>
			<DO name="Alm35" type="SPS_D"/>
			<DO name="Alm36" type="SPS_D"/>
			<DO name="Alm37" type="SPS_D"/>
			<DO name="Alm38" type="SPS_D"/>
			<DO name="Alm39" type="SPS_D"/>
			<DO name="Alm40" type="SPS_D"/>
			<DO name="Alm41" type="SPS_D"/>
			<DO name="Alm42" type="SPS_D"/>
			<DO name="Alm43" type="SPS_D"/>
			<DO name="Alm44" type="SPS_D"/>
			<DO name="Alm45" type="SPS_D"/>
			<DO name="Alm46" type="SPS_D"/>
			<DO name="Alm47" type="SPS_D"/>
			<DO name="Alm48" type="SPS_D"/>
			<DO name="Alm49" type="SPS_D"/>
			<DO name="Alm50" type="SPS_D"/>
			<DO name="Alm51" type="SPS_D"/>
			<DO name="Alm52" type="SPS_D"/>
			<DO name="Alm53" type="SPS_D"/>
			<DO name="Alm54" type="SPS_D"/>
			<DO name="Alm55" type="SPS_D"/>
			<DO name="Alm56" type="SPS_D"/>
			<DO name="Alm57" type="SPS_D"/>
			<DO name="Alm58" type="SPS_D"/>
			<DO name="Alm59" type="SPS_D"/>
			<DO name="Alm60" type="SPS_D"/>
			<DO name="Alm61" type="SPS_D"/>
			<DO name="Alm62" type="SPS_D"/>
			<DO name="Alm63" type="SPS_D"/>
			<DO name="Alm64" type="SPS_D"/>
			<DO name="Alm65" type="SPS_D"/>
			<DO name="Alm66" type="SPS_D"/>
			<DO name="Alm67" type="SPS_D"/>
			<DO name="Alm68" type="SPS_D"/>
			<DO name="Alm69" type="SPS_D"/>
			<DO name="Alm70" type="SPS_D"/>
			<DO name="Alm71" type="SPS_D"/>
			<DO name="Alm72" type="SPS_D"/>
			<DO name="Alm73" type="SPS_D"/>
			<DO name="Alm74" type="SPS_D"/>
			<DO name="Alm75" type="SPS_D"/>
			<DO name="Alm76" type="SPS_D"/>
			<DO name="Alm77" type="SPS_D"/>
			<DO name="Alm78" type="SPS_D"/>
			<DO name="Alm79" type="SPS_D"/>
			<DO name="Alm80" type="SPS_D"/>
			<DO name="Alm81" type="SPS_D"/>
			<DO name="Alm82" type="SPS_D"/>
			<DO name="Alm83" type="SPS_D"/>
			<DO name="Alm84" type="SPS_D"/>
			<DO name="Alm85" type="SPS_D"/>
			<DO name="Alm86" type="SPS_D"/>
			<DO name="Alm87" type="SPS_D"/>
			<DO name="Alm88" type="SPS_D"/>
			<DO name="Alm89" type="SPS_D"/>
			<DO name="Alm90" type="SPS_D"/>
			<DO name="Alm91" type="SPS_D"/>
			<DO name="Alm92" type="SPS_D"/>
			<DO name="Alm93" type="SPS_D"/>
			<DO name="Alm94" type="SPS_D"/>
			<DO name="Alm95" type="SPS_D"/>
			<DO name="Alm96" type="SPS_D"/>
		</LNodeType>
		<LNodeType id="GGIO_IND_11" desc="Generic Process I/O (w.r.t 11 Indication Elements)" lnClass="GGIO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Ind1" type="SPS_D"/>
			<DO name="Ind2" type="SPS_D"/>
			<DO name="Ind3" type="SPS_D"/>
			<DO name="Ind4" type="SPS_D"/>
			<DO name="Ind5" type="SPS_D"/>
			<DO name="Ind6" type="SPS_D"/>
			<DO name="Ind7" type="SPS_D"/>
			<DO name="Ind8" type="SPS_D"/>
			<DO name="Ind9" type="SPS_D"/>
			<DO name="Ind10" type="SPS_D"/>
			<DO name="Ind11" type="SPS_D"/>
		</LNodeType>
		<LNodeType id="GGIO_IND_12" desc="Generic Process I/O (w.r.t 12 Indication Elements)" lnClass="GGIO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Ind1" type="SPS_D"/>
			<DO name="Ind2" type="SPS_D"/>
			<DO name="Ind3" type="SPS_D"/>
			<DO name="Ind4" type="SPS_D"/>
			<DO name="Ind5" type="SPS_D"/>
			<DO name="Ind6" type="SPS_D"/>
			<DO name="Ind7" type="SPS_D"/>
			<DO name="Ind8" type="SPS_D"/>
			<DO name="Ind9" type="SPS_D"/>
			<DO name="Ind10" type="SPS_D"/>
			<DO name="Ind11" type="SPS_D"/>
			<DO name="Ind12" type="SPS_D"/>
		</LNodeType>
		<LNodeType id="GGIO_IND_13" desc="Generic process I/O (w.r.t 13 Indication Elements)" lnClass="GGIO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Ind1" type="SPS_D"/>
			<DO name="Ind2" type="SPS_D"/>
			<DO name="Ind3" type="SPS_D"/>
			<DO name="Ind4" type="SPS_D"/>
			<DO name="Ind5" type="SPS_D"/>
			<DO name="Ind6" type="SPS_D"/>
			<DO name="Ind7" type="SPS_D"/>
			<DO name="Ind8" type="SPS_D"/>
			<DO name="Ind9" type="SPS_D"/>
			<DO name="Ind10" type="SPS_D"/>
			<DO name="Ind11" type="SPS_D"/>
			<DO name="Ind12" type="SPS_D"/>
			<DO name="Ind13" type="SPS_D"/>
		</LNodeType>
		<LNodeType id="GGIO_IND_3" desc="Generic Process I/O (w.r.t 3 Indication Elements)" lnClass="GGIO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Ind1" type="SPS_D"/>
			<DO name="Ind2" type="SPS_D"/>
			<DO name="Ind3" type="SPS_D"/>
		</LNodeType>
		<LNodeType id="GGIO_IND_32" desc="Generic Process I/O (w.r.t 32 Indication Elements)" lnClass="GGIO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Ind1" type="SPS_D"/>
			<DO name="Ind2" type="SPS_D"/>
			<DO name="Ind3" type="SPS_D"/>
			<DO name="Ind4" type="SPS_D"/>
			<DO name="Ind5" type="SPS_D"/>
			<DO name="Ind6" type="SPS_D"/>
			<DO name="Ind7" type="SPS_D"/>
			<DO name="Ind8" type="SPS_D"/>
			<DO name="Ind9" type="SPS_D"/>
			<DO name="Ind10" type="SPS_D"/>
			<DO name="Ind11" type="SPS_D"/>
			<DO name="Ind12" type="SPS_D"/>
			<DO name="Ind13" type="SPS_D"/>
			<DO name="Ind14" type="SPS_D"/>
			<DO name="Ind15" type="SPS_D"/>
			<DO name="Ind16" type="SPS_D"/>
			<DO name="Ind17" type="SPS_D"/>
			<DO name="Ind18" type="SPS_D"/>
			<DO name="Ind19" type="SPS_D"/>
			<DO name="Ind20" type="SPS_D"/>
			<DO name="Ind21" type="SPS_D"/>
			<DO name="Ind22" type="SPS_D"/>
			<DO name="Ind23" type="SPS_D"/>
			<DO name="Ind24" type="SPS_D"/>
			<DO name="Ind25" type="SPS_D"/>
			<DO name="Ind26" type="SPS_D"/>
			<DO name="Ind27" type="SPS_D"/>
			<DO name="Ind28" type="SPS_D"/>
			<DO name="Ind29" type="SPS_D"/>
			<DO name="Ind30" type="SPS_D"/>
			<DO name="Ind31" type="SPS_D"/>
			<DO name="Ind32" type="SPS_D"/>
		</LNodeType>
		<LNodeType id="GGIO_IND_64" desc="Generic Process I/O (w.r.t 64 Indication Elements)" lnClass="GGIO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Ind1" type="SPS_D"/>
			<DO name="Ind2" type="SPS_D"/>
			<DO name="Ind3" type="SPS_D"/>
			<DO name="Ind4" type="SPS_D"/>
			<DO name="Ind5" type="SPS_D"/>
			<DO name="Ind6" type="SPS_D"/>
			<DO name="Ind7" type="SPS_D"/>
			<DO name="Ind8" type="SPS_D"/>
			<DO name="Ind9" type="SPS_D"/>
			<DO name="Ind10" type="SPS_D"/>
			<DO name="Ind11" type="SPS_D"/>
			<DO name="Ind12" type="SPS_D"/>
			<DO name="Ind13" type="SPS_D"/>
			<DO name="Ind14" type="SPS_D"/>
			<DO name="Ind15" type="SPS_D"/>
			<DO name="Ind16" type="SPS_D"/>
			<DO name="Ind17" type="SPS_D"/>
			<DO name="Ind18" type="SPS_D"/>
			<DO name="Ind19" type="SPS_D"/>
			<DO name="Ind20" type="SPS_D"/>
			<DO name="Ind21" type="SPS_D"/>
			<DO name="Ind22" type="SPS_D"/>
			<DO name="Ind23" type="SPS_D"/>
			<DO name="Ind24" type="SPS_D"/>
			<DO name="Ind25" type="SPS_D"/>
			<DO name="Ind26" type="SPS_D"/>
			<DO name="Ind27" type="SPS_D"/>
			<DO name="Ind28" type="SPS_D"/>
			<DO name="Ind29" type="SPS_D"/>
			<DO name="Ind30" type="SPS_D"/>
			<DO name="Ind31" type="SPS_D"/>
			<DO name="Ind32" type="SPS_D"/>
			<DO name="Ind33" type="SPS_D"/>
			<DO name="Ind34" type="SPS_D"/>
			<DO name="Ind35" type="SPS_D"/>
			<DO name="Ind36" type="SPS_D"/>
			<DO name="Ind37" type="SPS_D"/>
			<DO name="Ind38" type="SPS_D"/>
			<DO name="Ind39" type="SPS_D"/>
			<DO name="Ind40" type="SPS_D"/>
			<DO name="Ind41" type="SPS_D"/>
			<DO name="Ind42" type="SPS_D"/>
			<DO name="Ind43" type="SPS_D"/>
			<DO name="Ind44" type="SPS_D"/>
			<DO name="Ind45" type="SPS_D"/>
			<DO name="Ind46" type="SPS_D"/>
			<DO name="Ind47" type="SPS_D"/>
			<DO name="Ind48" type="SPS_D"/>
			<DO name="Ind49" type="SPS_D"/>
			<DO name="Ind50" type="SPS_D"/>
			<DO name="Ind51" type="SPS_D"/>
			<DO name="Ind52" type="SPS_D"/>
			<DO name="Ind53" type="SPS_D"/>
			<DO name="Ind54" type="SPS_D"/>
			<DO name="Ind55" type="SPS_D"/>
			<DO name="Ind56" type="SPS_D"/>
			<DO name="Ind57" type="SPS_D"/>
			<DO name="Ind58" type="SPS_D"/>
			<DO name="Ind59" type="SPS_D"/>
			<DO name="Ind60" type="SPS_D"/>
			<DO name="Ind61" type="SPS_D"/>
			<DO name="Ind62" type="SPS_D"/>
			<DO name="Ind63" type="SPS_D"/>
			<DO name="Ind64" type="SPS_D"/>
		</LNodeType>
		<LNodeType id="GGIO_IND_64_CTRL" desc="Generic process I/O (w.r.t 64 Indications Ctrl i/p)" lnClass="GGIO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="SPCSO1" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO2" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO3" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO4" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO5" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO6" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO7" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO8" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO9" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO10" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO11" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO12" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO13" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO14" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO15" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO16" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO17" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO18" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO19" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO20" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO21" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO22" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO23" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO24" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO25" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO26" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO27" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO28" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO29" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO30" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO31" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO32" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO33" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO34" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO35" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO36" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO37" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO38" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO39" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO40" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO41" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO42" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO43" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO44" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO45" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO46" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO47" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO48" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO49" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO50" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO51" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO52" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO53" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO54" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO55" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO56" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO57" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO58" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO59" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO60" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO61" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO62" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO63" type="SPC_CONTROL_NS"/>
			<DO name="SPCSO64" type="SPC_CONTROL_NS"/>
		</LNodeType>
		<LNodeType id="LLN0_PROT_P14N" desc="Protection Domain Logical Node 0 (No volts)" lnClass="LLN0">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LLN0"/>
			<DO name="OcpMod" type="INC_CTRL_PRV_NS"/>
			<DO name="OcpBeh" type="INS_BEH_D_PRIV"/>
			<DO name="NgcMod" type="INC_CTRL_PRV_NS"/>
			<DO name="NgcBeh" type="INS_BEH_D_PRIV"/>
			<DO name="EfmMod" type="INC_CTRL_PRV_NS"/>
			<DO name="EfmBeh" type="INS_BEH_D_PRIV"/>
			<DO name="EfdMod" type="INC_CTRL_PRV_NS"/>
			<DO name="EfdBeh" type="INS_BEH_D_PRIV"/>
			<DO name="SenMod" type="INC_CTRL_PRV_NS"/>
			<DO name="SenBeh" type="INS_BEH_D_PRIV"/>
			<DO name="ThmMod" type="INC_CTRL_PRV_NS"/>
			<DO name="ThmBeh" type="INS_BEH_D_PRIV"/>
			<DO name="CbfMod" type="INC_CTRL_PRV_NS"/>
			<DO name="CbfBeh" type="INS_BEH_D_PRIV"/>
		</LNodeType>
		<LNodeType id="LLN0_STANDARD" desc="General Logical Node 0" lnClass="LLN0">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LLN0"/>
		</LNodeType>
		<LNodeType id="LLN0_SYSTEM" desc="System Logical Node 0" lnClass="LLN0">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LLN0"/>
			<DO name="LEDRs" type="SPC_CONTROL_NS"/>
			<DO name="OrdRun" type="SPS_WD_PRIV"/>
			<DO name="SyncSt" type="SPS_WD_PRIV"/>
		</LNodeType>
		<LNodeType id="LPHD_STANDARD" desc="Px40 Physical Device Information" lnClass="LPHD">
			<DO name="PhyNam" type="DPL_STANDARD"/>
			<DO name="PhyHealth" type="INS_HEALTH"/>
			<DO name="Proxy" type="SPS_D"/>
			<DO name="PwrUp" type="SPS_D"/>
		</LNodeType>
		<LNodeType id="MMXU_FOURIER_P14N" desc="Standard measurements (w.r.t Fourier Values - P14N No Volts)" lnClass="MMXU">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Hz" type="MV_FLOAT"/>
			<DO name="A1" type="WYE_SEG_RES_D"/>
			<DO name="A2" type="WYE_RES_ANG_D"/>
			<DO name="A3" type="WYE_RES_ANG_D"/>
		</LNodeType>
		<LNodeType id="MMXU_RMS_P14N" desc="Standard measurements (w.r.t RMS Values - P14N) No Volts" lnClass="MMXU">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="A" type="WYE_SEG_D"/>
		</LNodeType>
		<LNodeType id="MSQI_ALL_P14N" desc="Sequence and imbalance (w.r.t Pos, Neq, Zero) No Volts" lnClass="MSQI">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="SeqA" type="SEQ_MAG_ANG"/>
			<DO name="ImbNgA" type="MV_FLOAT"/>
		</LNodeType>
		<LNodeType id="MSTA_I_W_VAR_P14N" desc="Metering Statistics (w.r.t Current, Real + Reactive Power - Average + Max values) No Volts" lnClass="MSTA">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="AvAmps1" type="MV_FLOAT_D"/>
			<DO name="AvAmps2" type="MV_FLOAT_D"/>
			<DO name="AvAmps3" type="MV_FLOAT_D"/>
			<DO name="AvAmps4" type="MV_FLOAT_D"/>
			<DO name="AvAmps5" type="MV_FLOAT_D"/>
			<DO name="AvAmps6" type="MV_FLOAT_D"/>
			<DO name="MaxAmps1" type="MV_FLOAT_D"/>
			<DO name="MaxAmps2" type="MV_FLOAT_D"/>
			<DO name="MaxAmps3" type="MV_FLOAT_D"/>
		</LNodeType>
		<LNodeType id="PTOC_NEU" desc="Timed Overcurrent (w.r.t Neutral)" lnClass="PTOC">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Str" type="ACD_NEU"/>
			<DO name="Op" type="ACT_NEU"/>
		</LNodeType>
		<LNodeType id="PTOC_NO_SEG" desc="Timed Overcurrent (w.r.t No Phase Segregation)" lnClass="PTOC">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Str" type="ACD_NO_SEG"/>
			<DO name="Op" type="ACT_NO_SEG"/>
		</LNodeType>
		<LNodeType id="PTOC_SEG" desc="Timed Overcurrent (w.r.t Phase Segregation)" lnClass="PTOC">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Str" type="ACD_SEG"/>
			<DO name="Op" type="ACT_SEG"/>
		</LNodeType>
		<LNodeType id="PTRC_NO_SEG" desc="Protection trip conditioning (w.r.t No Phase Segregation)" lnClass="PTRC">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Tr" type="ACT_NO_SEG"/>
			<DO name="Str" type="ACD_NO_SEG"/>
		</LNodeType>
		<LNodeType id="PTTR_NO_SEG" desc="Thermal overload (w.r.t No Phase Segregation)" lnClass="PTTR">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Amp" type="MV_FLOAT"/>
			<DO name="TmpRl" type="MV_FLOAT"/>
			<DO name="Op" type="ACT_NO_SEG"/>
			<DO name="AlmThm" type="ACT_NO_SEG"/>
			<DO name="MTRRs" type="SPC_CTRL_PRV_NS"/>
		</LNodeType>
		<LNodeType id="RBRF_EXTTRIP" desc="Breaker Failure (w.r.t External Tripping)" lnClass="RBRF">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="OpEx" type="ACT_NO_SEG"/>
		</LNodeType>
		<LNodeType id="RDRE_BASIC" desc="Disturbance Recorder function (w.r.t Mandatory Attributes only)" lnClass="RDRE">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="RcdMade" type="SPS_WD"/>
			<DO name="FltNum" type="INS_BASIC"/>
		</LNodeType>
		<LNodeType id="RFLO_BASIC" desc="Fault Locator (w.r.t Mandatory Attributes only)" lnClass="RFLO">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="FltZ" type="CMV_MAG_FLOAT"/>
			<DO name="FltDiskm" type="MV_FLOAT"/>
		</LNodeType>
		<LNodeType id="XCBR_BASIC" desc="Circuit Breaker (w.r.t Mandatory Attributes Only)" lnClass="XCBR">
			<DO name="Mod" type="INC_MOD"/>
			<DO name="Beh" type="INS_BEH"/>
			<DO name="Health" type="INS_HEALTH"/>
			<DO name="NamPlt" type="LPL_LN"/>
			<DO name="Loc" type="SPS_WD"/>
			<DO name="EEHealth" type="INS_HEALTH"/>
			<DO name="OpCnt" type="INS_BASIC"/>
			<DO name="Pos" type="DPC_CTRL_PRV_NS"/>
			<DO name="BlkOpn" type="SPC_STATUS"/>
			<DO name="BlkCls" type="SPC_STATUS"/>
			<DO name="Lock" type="SPC_CTRL_PRV_NS"/>
			<DO name="CBOpCap" type="INS_CBCAP"/>
		</LNodeType>
		<DOType id="ACD_NEU" desc="Directional Protection Activation Information (w.r.t Neutral)" cdc="ACD">
			<DA name="general" bType="BOOLEAN" fc="ST"/>
			<DA name="dirGeneral" bType="Enum" type="dir" fc="ST"/>
			<DA name="neut" bType="BOOLEAN" fc="ST"/>
			<DA name="dirNeut" bType="Enum" type="dir" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="ACD_NO_SEG" desc="Directional Protection Activation Information (w,r,t No Phase Segregation)" cdc="ACD">
			<DA name="general" bType="BOOLEAN" fc="ST"/>
			<DA name="dirGeneral" bType="Enum" type="dir" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="ACD_SEG" desc="Directional Protection Activation Information (w.r.t Phase Segregation)" cdc="ACD">
			<DA name="general" bType="BOOLEAN" fc="ST"/>
			<DA name="dirGeneral" bType="Enum" type="dir" fc="ST"/>
			<DA name="phsA" bType="BOOLEAN" fc="ST"/>
			<DA name="dirPhsA" bType="Enum" type="dir" fc="ST"/>
			<DA name="phsB" bType="BOOLEAN" fc="ST"/>
			<DA name="dirPhsB" bType="Enum" type="dir" fc="ST"/>
			<DA name="phsC" bType="BOOLEAN" fc="ST"/>
			<DA name="dirPhsC" bType="Enum" type="dir" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="ACT_NEU" desc="Protection Activation Information (w.r.t Neutral)" cdc="ACT">
			<DA name="general" bType="BOOLEAN" fc="ST"/>
			<DA name="neut" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="ACT_NO_SEG" desc="Protection Activation Information (w.r.t No Phase Segregation)" cdc="ACT">
			<DA name="general" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="ACT_SEG" desc="Protection Activation Information (w.r.t Phase Segregation)" cdc="ACT">
			<DA name="general" bType="BOOLEAN" fc="ST"/>
			<DA name="phsA" bType="BOOLEAN" fc="ST"/>
			<DA name="phsB" bType="BOOLEAN" fc="ST"/>
			<DA name="phsC" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="CMV_MAG_FLOAT" desc="Complex Measured value (w.r.t Floating Point Magnitude)" cdc="CMV">
			<DA name="cVal" bType="Struct" type="Vector_Magnitude_Float" fc="MX"/>
			<DA name="q" bType="Quality" fc="MX"/>
			<DA name="t" bType="Timestamp" fc="MX"/>
			<DA name="units" bType="Struct" type="Unit_Multiplier" fc="CF"/>
			<DA name="db" bType="INT32U" fc="CF"/>
			<DA name="rangeC" bType="Struct" type="RangeConfig_Deadband" fc="CF"/>
		</DOType>
		<DOType id="DPC_CTRL_PRV_NS" desc="Controllable Double Point (NAME SPACE)" cdc="DPC">
			<DA name="SBO" bType="VisString65" fc="CO"/>
			<DA name="SBOw" bType="Struct" type="DPC_CTRL_PRV_NS_SelectWithValue" fc="CO"/>
			<DA name="Oper" bType="Struct" type="DPC_CTRL_PRV_NS_Operate" fc="CO"/>
			<DA name="Cancel" bType="Struct" type="DPC_CTRL_PRV_NS_Cancel" fc="CO"/>
			<DA name="bypass" bType="Enum" type="Bypass" fc="CO"/>
			<DA name="origin" bType="Struct" type="Originator" fc="ST"/>
			<DA name="stVal" bType="Dbpos" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="ctlModel" bType="Enum" type="ctlModel" fc="CF"/>
			<DA name="sboTimeout" bType="INT32U" fc="CF"/>
			<DA name="cdcNs" bType="VisString255" fc="EX"/>
			<DA name="cdcName" bType="VisString255" fc="EX"/>
		</DOType>
		<DOType id="DPL_STANDARD" desc="Standard Device Name Plate" cdc="DPL">
			<DA name="vendor" bType="VisString255" fc="DC"/>
			<DA name="hwRev" bType="VisString255" fc="DC"/>
			<DA name="swRev" bType="VisString255" fc="DC"/>
			<DA name="serNum" bType="VisString255" fc="DC"/>
			<DA name="model" bType="VisString255" fc="DC"/>
			<DA name="location" bType="VisString255" fc="DC"/>
		</DOType>
		<DOType id="INC_CTRL_PRV_NS" desc="Controllable Integer Status NAME SPACE" cdc="INC">
			<DA name="SBO" bType="VisString65" fc="CO"/>
			<DA name="SBOw" bType="Struct" type="INC_CTRL_PRV_NS_SelectWithValue" fc="CO"/>
			<DA name="Oper" bType="Struct" type="INC_CTRL_PRV_NS_Operate" fc="CO"/>
			<DA name="Cancel" bType="Struct" type="INC_CTRL_PRV_NS_Cancel" fc="CO"/>
			<DA name="bypass" bType="Enum" type="Bypass" fc="CO"/>
			<DA name="stVal" bType="Enum" type="Mod" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="ctlModel" bType="Enum" type="ctlModel" fc="CF"/>
			<DA name="cdcNs" bType="VisString255" fc="EX"/>
			<DA name="cdcName" bType="VisString255" fc="EX"/>
			<DA name="dataNs" bType="VisString255" fc="EX"/>
		</DOType>
		<DOType id="INC_MOD" desc="Controllable Integer Status (w.r.t Mode)" cdc="INC">
			<DA name="stVal" bType="Enum" type="Mod" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="ctlModel" bType="Enum" type="ctlModel" fc="CF"/>
		</DOType>
		<DOType id="INS_BASIC" desc="Integer Status (w.r.t Mandatory Options Only)" cdc="INS">
			<DA name="stVal" bType="INT32" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="INS_BEH" desc="Integer Status (w.r.t Behaviour)" cdc="INS">
			<DA name="stVal" bType="Enum" type="Beh" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="INS_BEH_D_PRIV" desc="Integer Status (w.r.t Behaviour, with Description (Private DO))" cdc="INS">
			<DA name="stVal" bType="Enum" type="Beh" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="d" bType="VisString255" fc="DC"/>
			<DA name="dataNs" bType="VisString255" fc="EX"/>
		</DOType>
		<DOType id="INS_CBCAP" desc="Integer Status(w.r.t Circuit Breaker Operating)" cdc="INS">
			<DA name="stVal" bType="Enum" type="CBOpCap" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="INS_HEALTH" desc="Integer Status (w.r.t health)" cdc="INS">
			<DA name="stVal" bType="Enum" type="Health" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="LPL_LLN0" desc="Logical Node 0 Name Plate" cdc="LPL">
			<DA name="vendor" bType="VisString255" fc="DC"/>
			<DA name="swRev" bType="VisString255" fc="DC"/>
			<DA name="d" bType="VisString255" fc="DC"/>
			<DA name="configRev" bType="VisString255" fc="DC"/>
			<DA name="ldNs" bType="VisString255" fc="EX"/>
		</DOType>
		<DOType id="LPL_LN" desc="Standard Logical Node Name Plate" cdc="LPL">
			<DA name="vendor" bType="VisString255" fc="DC"/>
			<DA name="swRev" bType="VisString255" fc="DC"/>
			<DA name="d" bType="VisString255" fc="DC"/>
		</DOType>
		<DOType id="MV_FLOAT" desc="Measured value (w.r.t. Floating Point value)" cdc="MV">
			<DA name="mag" bType="Struct" type="AnalogueValue_Float" fc="MX"/>
			<DA name="q" bType="Quality" fc="MX"/>
			<DA name="t" bType="Timestamp" fc="MX"/>
			<DA name="units" bType="Struct" type="Unit_Multiplier" fc="CF"/>
			<DA name="db" bType="INT32U" fc="CF"/>
			<DA name="rangeC" bType="Struct" type="RangeConfig_Deadband" fc="CF"/>
		</DOType>
		<DOType id="MV_FLOAT_D" desc="Measured value (w.r.t Floating Point Value with Description)" cdc="MV">
			<DA name="mag" bType="Struct" type="AnalogueValue_Float" fc="MX"/>
			<DA name="q" bType="Quality" fc="MX"/>
			<DA name="t" bType="Timestamp" fc="MX"/>
			<DA name="units" bType="Struct" type="Unit_Multiplier" fc="CF"/>
			<DA name="db" bType="INT32U" fc="CF"/>
			<DA name="rangeC" bType="Struct" type="RangeConfig_Deadband" fc="CF"/>
			<DA name="d" bType="VisString255" fc="DC"/>
		</DOType>
		<DOType id="CMV_MAG_ANG_FLOAT" desc="Sequence components of a measurement value (w.r.t Magnitudes + Angles)" cdc="CMV">
			<DA name="cVal" bType="Struct" type="Vector_MagnitudeAngle_Float" fc="MX"/>
			<DA name="q" bType="Quality" fc="MX"/>
			<DA name="t" bType="Timestamp" fc="MX"/>
			<DA name="units" bType="Struct" type="Unit_Multiplier" fc="CF"/>
			<DA name="db" bType="INT32U" fc="CF"/>
			<DA name="rangeC" bType="Struct" type="RangeConfig_Deadband" fc="CF"/>
		</DOType>
		<DOType id="SEQ_MAG_ANG" desc="Complex Measured value (w.r.t Floating Point Magnitude and Angle)" cdc="SEQ">
			<SDO name="c1" desc="Sequence component 1 (For semantic meaning see seqT)" type="CMV_MAG_ANG_FLOAT"/>
			<SDO name="c2" desc="Sequence component 2 (For semantic meaning see seqT)" type="CMV_MAG_ANG_FLOAT"/>
			<SDO name="c3" desc="Sequence component 3 (For semantic meaning see seqT)" type="CMV_MAG_ANG_FLOAT"/>
			<DA name="seqT" bType="Enum" type="seqT" fc="MX"/>
		</DOType>
		<DOType id="SPC_CONTROL_NS" desc="Controllable Single Point (NAME SPACE)" cdc="SPC">
			<DA name="SBO" bType="VisString65" fc="CO"/>
			<DA name="SBOw" bType="Struct" type="SPC_CONTROL_NS_SelectWithValue" fc="CO"/>
			<DA name="Oper" bType="Struct" type="SPC_CONTROL_NS_Operate" fc="CO"/>
			<DA name="Cancel" bType="Struct" type="SPC_CONTROL_NS_Cancel" fc="CO"/>
			<DA name="bypass" bType="Enum" type="Bypass" fc="CO"/>
			<DA name="origin" bType="Struct" type="Originator" fc="ST"/>
			<DA name="stVal" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="ctlModel" bType="Enum" type="ctlModel" fc="CF"/>
			<DA name="sboTimeout" bType="INT32U" fc="CF"/>
			<DA name="cdcNs" bType="VisString255" fc="EX"/>
			<DA name="cdcName" bType="VisString255" fc="EX"/>
		</DOType>
		<DOType id="SPC_CTRL_PRV_NS" desc="Controllable Single Point (NAME SPACE)" cdc="SPC">
			<DA name="SBO" bType="VisString65" fc="CO"/>
			<DA name="SBOw" bType="Struct" type="SPC_CTRL_PRV_NS_SelectWithValue" fc="CO"/>
			<DA name="Oper" bType="Struct" type="SPC_CTRL_PRV_NS_Operate" fc="CO"/>
			<DA name="Cancel" bType="Struct" type="SPC_CTRL_PRV_NS_Cancel" fc="CO"/>
			<DA name="bypass" bType="Enum" type="Bypass" fc="CO"/>
			<DA name="origin" bType="Struct" type="Originator" fc="ST"/>
			<DA name="stVal" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="ctlModel" bType="Enum" type="ctlModel" fc="CF"/>
			<DA name="sboTimeout" bType="INT32U" fc="CF"/>
			<DA name="cdcNs" bType="VisString255" fc="EX"/>
			<DA name="cdcName" bType="VisString255" fc="EX"/>
			<DA name="dataNs" bType="VisString255" fc="EX"/>
		</DOType>
		<DOType id="SPC_STATUS" desc="Controllable Single Point (w.r.t Status Only)" cdc="SPC">
			<DA name="stVal" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="ctlModel" bType="Enum" type="ctlModel" fc="CF"/>
		</DOType>
		<DOType id="SPS_D" desc="Standard Single Point Status (with Description)" cdc="SPS">
			<DA name="stVal" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="d" bType="VisString255" fc="DC"/>
		</DOType>
		<DOType id="SPS_WD" desc="Single Point Status (without Description)" cdc="SPS">
			<DA name="stVal" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
		</DOType>
		<DOType id="SPS_WD_PRIV" desc="Single Point Status (without Description with Name Space))" cdc="SPS">
			<DA name="stVal" bType="BOOLEAN" fc="ST"/>
			<DA name="q" bType="Quality" fc="ST"/>
			<DA name="t" bType="Timestamp" fc="ST"/>
			<DA name="dataNs" bType="VisString255" fc="EX"/>
		</DOType>
		<DOType id="WYE_RES_ANG_D" desc="Phase to ground measurements for a 3-Phase system (w.r.t Residual + Description + Angle)" cdc="WYE">
			<SDO name="res" desc="Measurement values for the residual system current" type="CMV_MAG_ANG_FLOAT"/>
			<DA name="d" bType="VisString255" fc="DC"/>
		</DOType>
		<DOType id="WYE_SEG_D" desc="Phase to ground measurements for a 3-Phase system (w.r.t Phase Seggregation + Description)" cdc="WYE">
			<SDO name="phsA" desc="Measurement values for Phase A" type="CMV_MAG_FLOAT"/>
			<SDO name="phsB" desc="Measurement values for Phase B" type="CMV_MAG_FLOAT"/>
			<SDO name="phsC" desc="Measurement values for Phase C" type="CMV_MAG_FLOAT"/>
			<DA name="d" bType="VisString255" fc="DC"/>
		</DOType>
		<DOType id="WYE_SEG_RES_D" desc="Phase to ground measurements for a 3-Phase system (w.r.t Phase Seggregation + Residual + Description)" cdc="WYE">
			<SDO name="phsA" desc="Measurement values for Phase A" type="CMV_MAG_ANG_FLOAT"/>
			<SDO name="phsB" desc="Measurement values for Phase B" type="CMV_MAG_ANG_FLOAT"/>
			<SDO name="phsC" desc="Measurement values for Phase C" type="CMV_MAG_ANG_FLOAT"/>
			<SDO name="neut" desc="Measurement values for neutral input" type="CMV_MAG_ANG_FLOAT"/>
			<DA name="d" bType="VisString255" fc="DC"/>
		</DOType>
		<DAType id="DPC_CTRL_PRV_NS_SelectWithValue" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
			<BDA desc="Control element" name="Check" bType="Check"/>
		</DAType>
		<DAType id="DPC_CTRL_PRV_NS_Operate" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
			<BDA desc="Control element" name="Check" bType="Check"/>
		</DAType>
		<DAType id="DPC_CTRL_PRV_NS_Cancel" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
		</DAType>
		<DAType id="INC_CTRL_PRV_NS_SelectWithValue" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="INT32"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
			<BDA desc="Control element" name="Check" bType="Check"/>
		</DAType>
		<DAType id="INC_CTRL_PRV_NS_Operate" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="INT32"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
			<BDA desc="Control element" name="Check" bType="Check"/>
		</DAType>
		<DAType id="INC_CTRL_PRV_NS_Cancel" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="INT32"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
		</DAType>
		<DAType id="SPC_CONTROL_NS_SelectWithValue" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
			<BDA desc="Control element" name="Check" bType="Check"/>
		</DAType>
		<DAType id="SPC_CONTROL_NS_Operate" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
			<BDA desc="Control element" name="Check" bType="Check"/>
		</DAType>
		<DAType id="SPC_CONTROL_NS_Cancel" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
		</DAType>
		<DAType id="SPC_CTRL_PRV_NS_SelectWithValue" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
			<BDA desc="Control element" name="Check" bType="Check"/>
		</DAType>
		<DAType id="SPC_CTRL_PRV_NS_Operate" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
			<BDA desc="Control element" name="Check" bType="Check"/>
		</DAType>
		<DAType id="SPC_CTRL_PRV_NS_Cancel" desc="Control Object">
			<BDA desc="Control element" name="ctlVal" bType="BOOLEAN"/>
			<BDA desc="Control element" name="origin" bType="Struct" type="Originator"/>
			<BDA desc="Control element" name="ctlNum" bType="INT8U"/>
			<BDA desc="Control element" name="T" bType="Timestamp"/>
			<BDA desc="Control element" name="Test" bType="BOOLEAN"/>
		</DAType>
		<DAType id="AnalogueValue_Float" desc="General analogue value (w.r.t Floating Point value)">
			<BDA desc="Floating point value" name="f" bType="FLOAT32"/>
		</DAType>
		<DAType id="RangeConfig_Deadband" desc="Measurement range configuration">
			<BDA desc="High High range limit" name="hhLim" bType="Struct" type="AnalogueValue_Float"/>
			<BDA desc="High range limit" name="hLim" bType="Struct" type="AnalogueValue_Float"/>
			<BDA desc="Low range limit" name="lLim" bType="Struct" type="AnalogueValue_Float"/>
			<BDA desc="Low Low range limit" name="llLim" bType="Struct" type="AnalogueValue_Float"/>
			<BDA desc="Minimum process measurement for which values of i and f are considered within limits" name="min" bType="Struct" type="AnalogueValue_Float"/>
			<BDA desc="Maximum process measurement for which values of i and f are considered within limits" name="max" bType="Struct" type="AnalogueValue_Float"/>
		</DAType>
		<DAType id="Unit_Multiplier" desc="SI Unit definitions">
			<BDA desc="SI Unit" name="SIUnit" bType="Enum" type="SIUnit"/>
			<BDA desc="Multiplier value, the default of which is 0 (i.e. multiplier = 1)" name="multiplier" bType="Enum" type="multiplier"/>
		</DAType>
		<DAType id="Vector_Magnitude_Float" desc="Complex vector (w.r.t Floating Point Magnitude value)">
			<BDA desc="The magnitude of the complex value" name="mag" bType="Struct" type="AnalogueValue_Float"/>
		</DAType>
		<DAType id="Originator" desc="Originator of the last change of data attribute representing the value of a controllable data object">
			<BDA desc="Originator category (Not-supported, bay-control, station-control, remote-control, automatic-bay, automatic-station, automatic-remote, maintenance or process)" name="orCat" bType="Enum" type="orCategory"/>
			<BDA desc="Originator identification (Null value indicates unknown or not reported)" name="orIdent" bType="Octet64"/>
		</DAType>
		<DAType id="Vector_MagnitudeAngle_Float" desc="Complex vector (w.r.t Floating Point Magnitude and Angle values)">
			<BDA desc="The magnitude of the complex value" name="mag" bType="Struct" type="AnalogueValue_Float"/>
			<BDA desc="The angle of the complex value (the unit is degrees)" name="ang" bType="Struct" type="AnalogueValue_Float"/>
		</DAType>
		<EnumType id="AutoRecSt" desc="Auto-Reclose Status">
			<EnumVal ord="1">Ready</EnumVal>
			<EnumVal ord="2">InProgress</EnumVal>
			<EnumVal ord="3">Successful</EnumVal>
			<EnumVal ord="4">Unsuccessful</EnumVal>
		</EnumType>
		<EnumType id="Beh" desc="Behaviour">
			<EnumVal ord="1">on</EnumVal>
			<EnumVal ord="2">blocked</EnumVal>
			<EnumVal ord="3">test</EnumVal>
			<EnumVal ord="4">test/blocked</EnumVal>
			<EnumVal ord="5">off</EnumVal>
		</EnumType>
		<EnumType id="CBOpCap" desc="Enumeration for CB Operation">
			<EnumVal ord="1">None</EnumVal>
			<EnumVal ord="2">Open</EnumVal>
			<EnumVal ord="3">Close-Open</EnumVal>
			<EnumVal ord="4">Open-Close-Open</EnumVal>
			<EnumVal ord="5">Close-Open-Close-Open</EnumVal>
		</EnumType>
		<EnumType id="ctlModel" desc="Control Model">
			<EnumVal ord="0">status-only</EnumVal>
			<EnumVal ord="1">direct-with-normal-security</EnumVal>
			<EnumVal ord="2">sbo-with-normal-security</EnumVal>
			<EnumVal ord="3">direct-with-enhanced-security</EnumVal>
			<EnumVal ord="4">sbo-with-enhanced-security</EnumVal>
		</EnumType>
		<EnumType id="Dbpos" desc="Circuit Breaker position">
			<EnumVal ord="0">intermediate</EnumVal>
			<EnumVal ord="1">off</EnumVal>
			<EnumVal ord="2">on</EnumVal>
			<EnumVal ord="3">bad</EnumVal>
		</EnumType>
		<EnumType id="dir" desc="Direction">
			<EnumVal ord="0">unknown</EnumVal>
			<EnumVal ord="1">forward</EnumVal>
			<EnumVal ord="2">backward</EnumVal>
			<EnumVal ord="3">both</EnumVal>
		</EnumType>
		<EnumType id="Health" desc="Health">
			<EnumVal ord="1">Ok</EnumVal>
			<EnumVal ord="2">Warning</EnumVal>
			<EnumVal ord="3">Alarm</EnumVal>
		</EnumType>
		<EnumType id="Mod" desc="Mode">
			<EnumVal ord="1">on</EnumVal>
			<EnumVal ord="2">blocked</EnumVal>
			<EnumVal ord="3">test</EnumVal>
			<EnumVal ord="4">test/blocked</EnumVal>
			<EnumVal ord="5">off</EnumVal>
		</EnumType>
		<EnumType id="seqT" desc="Sequence Measurement Type">
			<EnumVal ord="0">pos-neg-zero</EnumVal>
			<EnumVal ord="1">dir-quad-zero</EnumVal>
		</EnumType>
		<EnumType id="multiplier" desc="Exponents of the multiplier value in base 10.">
			<EnumVal ord="-24">y</EnumVal>
			<EnumVal ord="-21">z</EnumVal>
			<EnumVal ord="-18">a</EnumVal>
			<EnumVal ord="-15">f</EnumVal>
			<EnumVal ord="-12">p</EnumVal>
			<EnumVal ord="-9">n</EnumVal>
			<EnumVal ord="-6">µ</EnumVal>
			<EnumVal ord="-3">m</EnumVal>
			<EnumVal ord="-2">c</EnumVal>
			<EnumVal ord="-1">d</EnumVal>
			<EnumVal ord="0"/>
			<EnumVal ord="1">da</EnumVal>
			<EnumVal ord="2">h</EnumVal>
			<EnumVal ord="3">k</EnumVal>
			<EnumVal ord="6">M</EnumVal>
			<EnumVal ord="9">G</EnumVal>
			<EnumVal ord="12">T</EnumVal>
			<EnumVal ord="15">P</EnumVal>
			<EnumVal ord="18">E</EnumVal>
			<EnumVal ord="21">Z</EnumVal>
			<EnumVal ord="24">Y</EnumVal>
		</EnumType>
		<EnumType id="orCategory" desc="orCategory">
			<EnumVal ord="0">not-supported</EnumVal>
			<EnumVal ord="1">bay-control</EnumVal>
			<EnumVal ord="2">station-control</EnumVal>
			<EnumVal ord="3">remote-control</EnumVal>
			<EnumVal ord="4">automatic-bay</EnumVal>
			<EnumVal ord="5">automatic-station</EnumVal>
			<EnumVal ord="6">automatic-remote</EnumVal>
			<EnumVal ord="7">maintenance</EnumVal>
			<EnumVal ord="8">process</EnumVal>
		</EnumType>
		<EnumType id="SIUnit" desc="SI Units derived from ISO/IEC 1000">
			<EnumVal ord="-16">years</EnumVal>
			<EnumVal ord="-15">months</EnumVal>
			<EnumVal ord="-14">weeks</EnumVal>
			<EnumVal ord="-13">V/s</EnumVal>
			<EnumVal ord="-12">mins</EnumVal>
			<EnumVal ord="-11">hours</EnumVal>
			<EnumVal ord="-10">days</EnumVal>
			<EnumVal ord="-9">°F</EnumVal>
			<EnumVal ord="-8">ratio</EnumVal>
			<EnumVal ord="-7">miles</EnumVal>
			<EnumVal ord="-6">inches</EnumVal>
			<EnumVal ord="-5">feet</EnumVal>
			<EnumVal ord="-4">df/dt</EnumVal>
			<EnumVal ord="-3">Hz/s</EnumVal>
			<EnumVal ord="-2">%</EnumVal>
			<EnumVal ord="-1">pu</EnumVal>
			<EnumVal ord="1">none</EnumVal>
			<EnumVal ord="2">m</EnumVal>
			<EnumVal ord="3">kg</EnumVal>
			<EnumVal ord="4">s</EnumVal>
			<EnumVal ord="5">A</EnumVal>
			<EnumVal ord="6">K</EnumVal>
			<EnumVal ord="7">mol</EnumVal>
			<EnumVal ord="8">cd</EnumVal>
			<EnumVal ord="9">deg</EnumVal>
			<EnumVal ord="10">rad</EnumVal>
			<EnumVal ord="11">sr</EnumVal>
			<EnumVal ord="21">Gy</EnumVal>
			<EnumVal ord="22">q</EnumVal>
			<EnumVal ord="23">°C</EnumVal>
			<EnumVal ord="24">Sv</EnumVal>
			<EnumVal ord="25">F</EnumVal>
			<EnumVal ord="26">C</EnumVal>
			<EnumVal ord="27">S</EnumVal>
			<EnumVal ord="28">H</EnumVal>
			<EnumVal ord="29">V</EnumVal>
			<EnumVal ord="30">ohm</EnumVal>
			<EnumVal ord="31">J</EnumVal>
			<EnumVal ord="32">N</EnumVal>
			<EnumVal ord="33">Hz</EnumVal>
			<EnumVal ord="34">lx</EnumVal>
			<EnumVal ord="35">Lm</EnumVal>
			<EnumVal ord="36">Wb</EnumVal>
			<EnumVal ord="37">T</EnumVal>
			<EnumVal ord="38">W</EnumVal>
			<EnumVal ord="39">Pa</EnumVal>
			<EnumVal ord="41">m²</EnumVal>
			<EnumVal ord="42">m³</EnumVal>
			<EnumVal ord="43">m/s</EnumVal>
			<EnumVal ord="44">m/s²</EnumVal>
			<EnumVal ord="45">m³/s</EnumVal>
			<EnumVal ord="46">m/m³</EnumVal>
			<EnumVal ord="47">M</EnumVal>
			<EnumVal ord="48">kg/m³</EnumVal>
			<EnumVal ord="49">m²/s</EnumVal>
			<EnumVal ord="50">W/m K</EnumVal>
			<EnumVal ord="51">J/K</EnumVal>
			<EnumVal ord="52">ppm</EnumVal>
			<EnumVal ord="53">1/s</EnumVal>
			<EnumVal ord="54">rad/s</EnumVal>
			<EnumVal ord="61">VA</EnumVal>
			<EnumVal ord="62">Watts</EnumVal>
			<EnumVal ord="63">VAr</EnumVal>
			<EnumVal ord="64">phi</EnumVal>
			<EnumVal ord="65">cos(phi)</EnumVal>
			<EnumVal ord="66">Vs</EnumVal>
			<EnumVal ord="67">V²</EnumVal>
			<EnumVal ord="68">As</EnumVal>
			<EnumVal ord="69">A²</EnumVal>
			<EnumVal ord="70">A²t</EnumVal>
			<EnumVal ord="71">VAh</EnumVal>
			<EnumVal ord="72">Wh</EnumVal>
			<EnumVal ord="73">VArh</EnumVal>
			<EnumVal ord="74">V/Hz</EnumVal>
		</EnumType>
		<EnumType id="AddCause" desc="AddCause">
			<EnumVal ord="0">Unknown</EnumVal>
			<EnumVal ord="1">Not-supported</EnumVal>
			<EnumVal ord="2">Blocked-by-switching-hierarchy</EnumVal>
			<EnumVal ord="3">Select-failed</EnumVal>
			<EnumVal ord="4">Invalid-position</EnumVal>
			<EnumVal ord="5">Position-reached</EnumVal>
			<EnumVal ord="6">Parameter-change-in-execution</EnumVal>
			<EnumVal ord="7">Step-limit</EnumVal>
			<EnumVal ord="8">Blocked-by-Mode</EnumVal>
			<EnumVal ord="9">Blocked-by-process</EnumVal>
			<EnumVal ord="10">Blocked-by-interlocking</EnumVal>
			<EnumVal ord="11">Blocked-by-synchrocheck</EnumVal>
			<EnumVal ord="12">Command-already-in-execution</EnumVal>
			<EnumVal ord="13">Blocked-by-health</EnumVal>
			<EnumVal ord="14">1-of-n-control</EnumVal>
			<EnumVal ord="15">Abortion-by-cancel</EnumVal>
			<EnumVal ord="16">Time-limit-over</EnumVal>
			<EnumVal ord="17">Abortion-by-trip</EnumVal>
			<EnumVal ord="18">Object-not-selected</EnumVal>
		</EnumType>
		<EnumType id="Error" desc="Error">
			<EnumVal ord="0">No error</EnumVal>
			<EnumVal ord="1">Unknown</EnumVal>
			<EnumVal ord="2">Timeout Test Not OK</EnumVal>
			<EnumVal ord="3">Operator test Not OK</EnumVal>
		</EnumType>
		<EnumType id="Bypass" desc="Bypass">
			<EnumVal ord="0">locking-bypass</EnumVal>
			<EnumVal ord="1">mode-bypass</EnumVal>
			<EnumVal ord="2">automation-bypass</EnumVal>
			<EnumVal ord="3">uniqueness-bypass</EnumVal>
			<EnumVal ord="4">select-bypass</EnumVal>
			<EnumVal ord="5">status-bypass</EnumVal>
		</EnumType>
	</DataTypeTemplates>
</SCL>
