/**
 * @file protocol_client_impl.cpp
 * @brief IEC 60870-5-103 协议客户端实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/client/protocol_client_impl.hpp"

#include "zexuan/protocol/protocol_client/client/tcp_connection_manager.hpp"
#include "zexuan/protocol/protocol_client/gateway/message_frame_parser.hpp"
#include "zexuan/protocol/protocol_client/transform/message_type_handler_factory.hpp"

// 包含所有消息处理器
#include "zexuan/protocol/protocol_client/transform/handlers/type1_handler.hpp"
#include "zexuan/protocol/protocol_client/transform/handlers/type2_handler.hpp"
#include "zexuan/protocol/protocol_client/transform/handlers/type3_handler.hpp"
#include "zexuan/protocol/protocol_client/transform/handlers/type4_handler.hpp"
#include "zexuan/protocol/protocol_client/transform/handlers/type5_handler.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace client {

    ProtocolClientImpl::ProtocolClientImpl(const std::string& config_file_path)
        : LifecycleComponentBase("ProtocolClient"), 
          config_file_path_(config_file_path) {
        
        // 初始化日志器
        logger_ = std::make_unique<DefaultClientLogger>();
        
        spdlog::info("{}: ProtocolClient created, config file: {}", 
                    GetComponentName(), config_file_path_);
    }

    ProtocolClientImpl::~ProtocolClientImpl() {
        // 确保在析构前调用Stop()
        if (IsRunning()) {
            spdlog::warn("{}: Client not stopped before destruction, forcing stop", GetComponentName());
            Stop();
        }
        spdlog::info("{}: ProtocolClient destroyed", GetComponentName());
    }

    bool ProtocolClientImpl::Start() {
        if (IsRunning()) {
            spdlog::warn("{}: Client is already running", GetComponentName());
            return true;
        }

        if (!TransitionTo(base::ComponentState::STARTING)) {
            return false;
        }

        try {
            spdlog::info("{}: Starting protocol client", GetComponentName());

            // 1. 初始化日志系统
            if (!Initialize(GetComponentName(), config_file_path_)) {
                spdlog::error("{}: Failed to initialize logger", GetComponentName());
                TransitionTo(base::ComponentState::ERROR);
                return false;
            }

            // 2. 加载配置
            spdlog::debug("{}: Loading configuration", GetComponentName());
            if (!LoadConfiguration()) {
                spdlog::error("{}: Failed to load configuration", GetComponentName());
                TransitionTo(base::ComponentState::ERROR);
                return false;
            }

            // 3. 创建核心组件
            spdlog::debug("{}: Creating core components", GetComponentName());
            if (!CreateCoreComponents()) {
                spdlog::error("{}: Failed to create core components", GetComponentName());
                TransitionTo(base::ComponentState::ERROR);
                return false;
            }

            // 4. 初始化Mediator和组件
            spdlog::debug("{}: Initializing mediator and components", GetComponentName());
            if (!InitializeMediatorAndComponents()) {
                spdlog::error("{}: Failed to initialize mediator and components", GetComponentName());
                TransitionTo(base::ComponentState::ERROR);
                return false;
            }

            // 5. 注册内置消息处理器
            spdlog::debug("{}: Registering builtin handlers", GetComponentName());
            if (!RegisterBuiltinHandlers()) {
                spdlog::error("{}: Failed to register builtin handlers", GetComponentName());
                TransitionTo(base::ComponentState::ERROR);
                return false;
            }

            is_running_ = true;
            UpdateConnectionState(ConnectionState::DISCONNECTED, "Client started, ready to connect");

            if (!TransitionTo(base::ComponentState::RUNNING)) {
                return false;
            }

            spdlog::info("{}: Protocol client started successfully", GetComponentName());
            return true;

        } catch (const std::exception& e) {
            spdlog::error("{}: Exception during start: {}", GetComponentName(), e.what());
            TransitionTo(base::ComponentState::ERROR);
            return false;
        }
    }

    bool ProtocolClientImpl::Stop() {
        if (!IsRunning()) {
            return true;
        }

        if (!TransitionTo(base::ComponentState::STOPPING)) {
            return false;
        }

        spdlog::info("{}: Stopping protocol client", GetComponentName());

        try {
            // 1. 断开连接
            if (IsConnected()) {
                spdlog::debug("{}: Disconnecting from server", GetComponentName());
                Disconnect();
            }

            // 2. 清理组件

            // 3. 清理组件
            spdlog::debug("{}: Cleaning up components", GetComponentName());
            
            // 停止并清理TCP连接管理器
            if (connection_manager_) {
                connection_manager_->Stop();
                connection_manager_.reset();
            }
            
            // 清理消息处理相关组件
            handler_factory_.reset();
            frame_parser_.reset();
            


            is_running_ = false;
            UpdateConnectionState(ConnectionState::DISCONNECTED, "Client stopped");

            if (!TransitionTo(base::ComponentState::STOPPED)) {
                return false;
            }

            spdlog::info("{}: Protocol client stopped successfully", GetComponentName());
            return true;

        } catch (const std::exception& e) {
            spdlog::error("{}: Exception during stop: {}", GetComponentName(), e.what());
            return false;
        }
    }

    bool ProtocolClientImpl::IsRunning() const {
        return is_running_.load();
    }

    bool ProtocolClientImpl::IsConnected() const {
        return connection_state_.load() == ConnectionState::CONNECTED;
    }

    ConnectionState ProtocolClientImpl::GetConnectionState() const {
        return connection_state_.load();
    }

    bool ProtocolClientImpl::Connect() {
        if (!IsRunning()) {
            spdlog::error("{}: Cannot connect - client not running", GetComponentName());
            return false;
        }

        if (IsConnected()) {
            spdlog::debug("{}: Already connected", GetComponentName());
            return true;
        }

        if (!connection_manager_) {
            spdlog::error("{}: Connection manager not initialized", GetComponentName());
            return false;
        }

        spdlog::info("{}: Connecting to server {}:{}", GetComponentName(),
                    config_.server_host, config_.server_port);

        // 设置连接管理器回调
        SetupConnectionCallbacks();

        // 启动连接管理器
        if (!connection_manager_->Start()) {
            spdlog::error("{}: Failed to start connection manager", GetComponentName());
            return false;
        }

        // 发起连接
        if (!connection_manager_->Connect()) {
            spdlog::error("{}: Failed to initiate connection", GetComponentName());
            return false;
        }

        // 更新统计信息
        UpdateStatistics([](Statistics& stats) {
            stats.connection_attempts++;
        });

        return true;
    }

    void ProtocolClientImpl::Disconnect() {
        if (!IsConnected()) {
            spdlog::debug("{}: Already disconnected", GetComponentName());
            return;
        }

        if (!connection_manager_) {
            spdlog::error("{}: Connection manager not initialized", GetComponentName());
            return;
        }

        spdlog::info("{}: Disconnecting from server", GetComponentName());
        connection_manager_->Disconnect();
    }

    base::Result<void> ProtocolClientImpl::SendMessage(uint8_t message_type, const MessageRequest& request) {
        if (!IsRunning()) {
            return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }

        if (!IsConnected()) {
            return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }

        if (!handler_factory_) {
            return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }

        try {
            // 从工厂获取对应的消息处理器
            auto handler = handler_factory_->GetHandler(message_type);
            if (!handler) {
                spdlog::error("{}: No handler found for message type: {}", GetComponentName(), message_type);
                return std::unexpected(base::ErrorCode::OPERATION_FAILED);
            }

            // 创建消息
            auto message_result = handler->CreateMessage(message_type, request);
            if (!message_result) {
                spdlog::error("{}: Failed to create message for type: {}", GetComponentName(), message_type);
                return std::unexpected(base::ErrorCode::OPERATION_FAILED);
            }

            // 序列化消息为IEC 60870-5-103格式
            std::vector<uint8_t> serialized_data;
            size_t serialized_size = message_result.value().serialize(serialized_data);
            
            if (serialized_size == 0) {
                spdlog::error("{}: Failed to serialize message for type: {}", GetComponentName(), message_type);
                return std::unexpected(base::ErrorCode::OPERATION_FAILED);
            }

            // 发送序列化的数据（通过TCP连接管理器）
            if (!connection_manager_) {
                return std::unexpected(base::ErrorCode::OPERATION_FAILED);
            }

            bool send_success = connection_manager_->SendData(serialized_data.data(), serialized_size);
            if (!send_success) {
                spdlog::error("{}: Failed to send message for type: {}", GetComponentName(), message_type);
                return std::unexpected(base::ErrorCode::OPERATION_FAILED);
            }

            // 输出发送的字节（与原客户端格式一致）
            printf(">>> Sent Message Type %d (%zu bytes): ", message_type, serialized_size);
            for (uint8_t byte : serialized_data) {
                printf("%02X ", byte);
            }
            printf("\n");

            // 更新统计信息
            UpdateStatistics([](Statistics& stats) {
                stats.messages_sent++;
            });

            spdlog::info("{}: Message type {} sent successfully", GetComponentName(), message_type);
            return {};

        } catch (const std::exception& e) {
            spdlog::error("{}: Exception in SendMessage: {}", GetComponentName(), e.what());
            return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }
    }

    base::Result<void> ProtocolClientImpl::SendRawMessage(const std::string& message) {
        if (!IsRunning()) {
            return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }

        if (!IsConnected()) {
            return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }

        // TODO: 实现原始消息发送逻辑（将在TCP连接管理器中实现）
        spdlog::warn("{}: SendRawMessage method not yet implemented", GetComponentName());
        return std::unexpected(base::ErrorCode::OPERATION_FAILED);
    }

    // 接口方法的完整实现
    bool ProtocolClientImpl::RegisterMessageHandler(std::unique_ptr<IMessageTypeHandler> handler) {
        if (!handler_factory_) {
            spdlog::error("{}: Handler factory not initialized", GetComponentName());
            return false;
        }

        if (!handler) {
            spdlog::error("{}: Cannot register null handler", GetComponentName());
            return false;
        }

        auto supported_types = handler->GetSupportedTypes();
        bool success = handler_factory_->RegisterHandler(std::move(handler));
        
        if (success && !supported_types.empty()) {
            spdlog::info("{}: Successfully registered handler for types: {}", 
                        GetComponentName(), supported_types[0]);
        }
        
        return success;
    }

    bool ProtocolClientImpl::UnregisterMessageHandler(uint8_t message_type) {
        if (!handler_factory_) {
            spdlog::error("{}: Handler factory not initialized", GetComponentName());
            return false;
        }

        bool success = handler_factory_->UnregisterHandler(message_type);
        if (success) {
            spdlog::info("{}: Successfully unregistered handler for type: {}", GetComponentName(), message_type);
        }
        
        return success;
    }

    bool ProtocolClientImpl::SupportMessageType(uint8_t message_type) const {
        if (!handler_factory_) {
            return false;
        }

        return handler_factory_->HasHandler(message_type);
    }

    std::vector<uint8_t> ProtocolClientImpl::GetSupportedMessageTypes() const {
        if (!handler_factory_) {
            return {};
        }

        return handler_factory_->GetSupportedTypes();
    }

    void ProtocolClientImpl::SetConnectionStateCallback(ConnectionStateCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        connection_state_callback_ = callback;
    }

    void ProtocolClientImpl::SetMessageResponseCallback(MessageResponseCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        message_response_callback_ = callback;
    }

    void ProtocolClientImpl::SetRawDataReceivedCallback(RawDataReceivedCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        raw_data_received_callback_ = callback;
    }

    const ClientConfig& ProtocolClientImpl::GetConfig() const {
        return config_;
    }

    IProtocolClient::Statistics ProtocolClientImpl::GetStatistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return statistics_;
    }

    // IClientLogger接口实现
    bool ProtocolClientImpl::Initialize(const std::string& component_name, 
                                       const std::string& config_file_path) {
        return logger_->Initialize(component_name, config_file_path);
    }

    std::string ProtocolClientImpl::GetComponentName() const {
        return logger_ ? logger_->GetComponentName() : "ProtocolClient";
    }

    // 私有方法实现
    bool ProtocolClientImpl::LoadConfiguration() {
        if (!config_.LoadFromFile(config_file_path_)) {
            spdlog::error("{}: Failed to load configuration from {}", GetComponentName(), config_file_path_);
            return false;
        }
        return true;
    }

    bool ProtocolClientImpl::CreateCoreComponents() {
        try {

            // 创建TCP连接管理器
            connection_manager_ = std::make_unique<TcpConnectionManager>(config_);
            if (!connection_manager_) {
                spdlog::error("{}: Failed to create connection manager", GetComponentName());
                return false;
            }

            // 创建消息帧解析器
            frame_parser_ = std::make_unique<MessageFrameParser>();
            if (!frame_parser_) {
                spdlog::error("{}: Failed to create frame parser", GetComponentName());
                return false;
            }

            // 创建消息处理器工厂
            handler_factory_ = std::make_unique<MessageTypeHandlerFactory>();
            if (!handler_factory_) {
                spdlog::error("{}: Failed to create handler factory", GetComponentName());
                return false;
            }

            spdlog::debug("{}: Core components created successfully", GetComponentName());
            return true;

        } catch (const std::exception& e) {
            spdlog::error("{}: Exception creating core components: {}", GetComponentName(), e.what());
            return false;
        }
    }

    bool ProtocolClientImpl::InitializeMediatorAndComponents() {
        spdlog::debug("{}: Components initialized successfully", GetComponentName());
        return true;
    }

    bool ProtocolClientImpl::RegisterBuiltinHandlers() {
        if (!handler_factory_) {
            spdlog::error("{}: Handler factory not initialized", GetComponentName());
            return false;
        }

        try {
            // 注册Type 1-5的默认消息处理器
            spdlog::debug("{}: Registering default message handlers (Type 1-5)", GetComponentName());
            
            // 注册Type 1处理器（单帧测试）
            auto type1_handler = std::make_unique<Type1Handler>();
            if (!handler_factory_->RegisterHandler(std::move(type1_handler))) {
                spdlog::error("{}: Failed to register Type 1 handler", GetComponentName());
                return false;
            }

            // 注册Type 2处理器（多帧测试）
            auto type2_handler = std::make_unique<Type2Handler>();
            if (!handler_factory_->RegisterHandler(std::move(type2_handler))) {
                spdlog::error("{}: Failed to register Type 2 handler", GetComponentName());
                return false;
            }

            // 注册Type 3处理器（时间戳事件）
            auto type3_handler = std::make_unique<Type3Handler>();
            if (!handler_factory_->RegisterHandler(std::move(type3_handler))) {
                spdlog::error("{}: Failed to register Type 3 handler", GetComponentName());
                return false;
            }

            // 注册Type 4处理器（本地状态）
            auto type4_handler = std::make_unique<Type4Handler>();
            if (!handler_factory_->RegisterHandler(std::move(type4_handler))) {
                spdlog::error("{}: Failed to register Type 4 handler", GetComponentName());
                return false;
            }

            // 注册Type 5处理器（重命名请求）
            auto type5_handler = std::make_unique<Type5Handler>();
            if (!handler_factory_->RegisterHandler(std::move(type5_handler))) {
                spdlog::error("{}: Failed to register Type 5 handler", GetComponentName());
                return false;
            }

            spdlog::info("{}: Successfully registered {} built-in message handlers", 
                        GetComponentName(), handler_factory_->GetRegisteredTypesCount());
            return true;

        } catch (const std::exception& e) {
            spdlog::error("{}: Exception during handler registration: {}", GetComponentName(), e.what());
            return false;
        }
    }

    void ProtocolClientImpl::UpdateConnectionState(ConnectionState new_state, const std::string& description) {
        ConnectionState old_state = connection_state_.exchange(new_state);
        if (old_state != new_state) {
            spdlog::info("{}: Connection state changed: {} -> {}, {}", 
                        GetComponentName(), static_cast<int>(old_state), 
                        static_cast<int>(new_state), description);
            OnConnectionStateChanged(new_state, description);
        }
    }

    void ProtocolClientImpl::OnConnectionStateChanged(ConnectionState state, const std::string& description) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (connection_state_callback_) {
            try {
                connection_state_callback_(state, description);
            } catch (const std::exception& e) {
                spdlog::error("{}: Exception in connection state callback: {}", GetComponentName(), e.what());
            }
        }
    }

    void ProtocolClientImpl::OnMessageResponseReceived(uint8_t message_type, const MessageResult& result) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (message_response_callback_) {
            try {
                message_response_callback_(message_type, result);
            } catch (const std::exception& e) {
                spdlog::error("{}: Exception in message response callback: {}", GetComponentName(), e.what());
            }
        }
    }

    void ProtocolClientImpl::UpdateStatistics(const std::function<void(Statistics&)>& updater) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        updater(statistics_);
    }

    // 连接相关私有方法实现
    void ProtocolClientImpl::SetupConnectionCallbacks() {
        if (!connection_manager_) {
            return;
        }

        // 设置连接状态变化回调
        connection_manager_->SetConnectionEventCallback(
            [this](ConnectionState state, const std::string& description) {
                this->OnTcpConnectionStateChanged(state, description);
            });

        // 设置数据接收回调
        connection_manager_->SetDataReceivedCallback(
            [this](const uint8_t* data, size_t size) {
                this->OnTcpDataReceived(data, size);
            });

        // 设置帧解析器回调
        if (frame_parser_) {
            frame_parser_->SetCompleteFrameCallback(
                [this](const base::Message& message) {
                    this->ProcessReceivedMessage(message);
                });

            frame_parser_->SetRawFrameCallback(
                [this](const std::vector<uint8_t>& frame_data) {
                    this->ProcessRawFrame(frame_data);
                });
        }
    }

    void ProtocolClientImpl::OnTcpConnectionStateChanged(ConnectionState state, const std::string& description) {
        // 更新内部连接状态
        UpdateConnectionState(state, description);
    }

    void ProtocolClientImpl::OnTcpDataReceived(const uint8_t* data, size_t size) {
        if (!data || size == 0) {
            return;
        }

        // 更新统计信息
        UpdateStatistics([size](Statistics& stats) {
            stats.bytes_received += size;
        });

        // 优先使用原始数据回调（确保与原客户端行为一致）
        {
            std::lock_guard<std::mutex> lock(callback_mutex_);
            if (raw_data_received_callback_) {
                try {
                    raw_data_received_callback_(data, size);
                    return; // 如果有原始数据回调，就不使用帧解析器
                } catch (const std::exception& e) {
                    spdlog::error("{}: Exception in raw data callback: {}", GetComponentName(), e.what());
                }
            }
        }

        // 如果没有原始数据回调，则使用帧解析器（保持向后兼容）
        if (frame_parser_) {
            frame_parser_->ProcessData(data, size);
        }
    }

    void ProtocolClientImpl::ProcessReceivedMessage(const base::Message& message) {
        uint8_t message_type = message.getTyp();
        
        spdlog::debug("{}: Processing received message type: {}", GetComponentName(), message_type);

        // 使用消息处理器工厂处理消息
        if (handler_factory_ && handler_factory_->HasHandler(message_type)) {
            auto handler = handler_factory_->GetHandler(message_type);
            if (handler) {
                auto result = handler->ProcessResponse(message);
                if (result) {
                    OnMessageResponseReceived(message_type, result.value());
                } else {
                    spdlog::warn("{}: Failed to process message type {}", GetComponentName(), message_type);
                    MessageResult error_result;
                    error_result.success = false;
                    error_result.description = "Handler processing failed";
                    OnMessageResponseReceived(message_type, error_result);
                }
            }
        } else {
            spdlog::debug("{}: No handler found for message type: {}", GetComponentName(), message_type);
        }
    }

    void ProtocolClientImpl::ProcessRawFrame(const std::vector<uint8_t>& frame_data) {
        // 对于无法解析为IEC103消息的原始帧，记录日志
        spdlog::debug("{}: Received raw frame of {} bytes", GetComponentName(), frame_data.size());
        
        // 可以在这里添加原始帧处理逻辑
        std::string frame_str(frame_data.begin(), frame_data.end());
        spdlog::debug("{}: Raw frame as string: {}", GetComponentName(), frame_str);
    }

} // namespace client
} // namespace protocol
} // namespace zexuan