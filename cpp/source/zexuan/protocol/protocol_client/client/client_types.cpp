/**
 * @file client_types.cpp
 * @brief 客户端通用类型实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/client/client_types.hpp"
#include "zexuan/base/logger_manager.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace client {

    std::string ToString(ClientErrorCode error_code) {
        switch (error_code) {
            case ClientErrorCode::SUCCESS:
                return "Success";

            // 配置相关错误
            case ClientErrorCode::CONFIG_FILE_NOT_FOUND:
                return "Configuration file not found";
            case ClientErrorCode::CONFIG_PARSE_ERROR:
                return "Configuration file parse error";
            case ClientErrorCode::CONFIG_VALIDATION_ERROR:
                return "Configuration validation failed";
            case ClientErrorCode::CONFIG_MISSING_REQUIRED:
                return "Missing required configuration";

            // 连接相关错误
            case ClientErrorCode::CONNECTION_FAILED:
                return "Connection failed";
            case ClientErrorCode::CONNECTION_TIMEOUT:
                return "Connection timeout";
            case ClientErrorCode::CONNECTION_LOST:
                return "Connection lost";
            case ClientErrorCode::CONNECTION_REFUSED:
                return "Connection refused";
            case ClientErrorCode::ALREADY_CONNECTED:
                return "Already connected";
            case ClientErrorCode::NOT_CONNECTED:
                return "Not connected";

            // 消息相关错误
            case ClientErrorCode::MESSAGE_TYPE_NOT_SUPPORTED:
                return "Message type not supported";
            case ClientErrorCode::MESSAGE_CREATION_FAILED:
                return "Message creation failed";
            case ClientErrorCode::MESSAGE_SEND_FAILED:
                return "Message send failed";
            case ClientErrorCode::MESSAGE_PARSE_FAILED:
                return "Message parse failed";
            case ClientErrorCode::MESSAGE_VALIDATION_FAILED:
                return "Message validation failed";
            case ClientErrorCode::MESSAGE_TIMEOUT:
                return "Message timeout";

            // 处理器相关错误
            case ClientErrorCode::HANDLER_NOT_FOUND:
                return "Message handler not found";
            case ClientErrorCode::HANDLER_REGISTER_FAILED:
                return "Handler registration failed";
            case ClientErrorCode::HANDLER_ALREADY_EXISTS:
                return "Handler already exists";
            case ClientErrorCode::HANDLER_EXECUTION_FAILED:
                return "Handler execution failed";

            // 系统相关错误
            case ClientErrorCode::COMPONENT_NOT_INITIALIZED:
                return "Component not initialized";
            case ClientErrorCode::COMPONENT_START_FAILED:
                return "Component start failed";
            case ClientErrorCode::COMPONENT_STOP_FAILED:
                return "Component stop failed";
            case ClientErrorCode::RESOURCE_ALLOCATION_FAILED:
                return "Resource allocation failed";
            case ClientErrorCode::THREAD_CREATION_FAILED:
                return "Thread creation failed";

            // 通用错误
            case ClientErrorCode::INVALID_PARAMETER:
                return "Invalid parameter";
            case ClientErrorCode::OPERATION_NOT_SUPPORTED:
                return "Operation not supported";
            case ClientErrorCode::INTERNAL_ERROR:
                return "Internal error";
            case ClientErrorCode::UNKNOWN_ERROR:
            default:
                return "Unknown error";
        }
    }

    bool DefaultClientLogger::Initialize(const std::string& component_name, 
                                       const std::string& config_file_path) {
        if (initialized_) {
            spdlog::warn("{}: Logger already initialized", component_name);
            return true;
        }

        component_name_ = component_name;
        
        // 使用LoggerManager初始化日志系统
        std::string log_filename = component_name + "_client.log";
        if (!base::LoggerManager::initialize(log_filename, config_file_path)) {
            // 如果初始化失败，仍然可以使用默认的spdlog配置
            spdlog::warn("{}: Failed to initialize logger from config, using default", component_name_);
        }

        initialized_ = true;
        spdlog::info("{}: Logger initialized successfully", component_name_);
        return true;
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
