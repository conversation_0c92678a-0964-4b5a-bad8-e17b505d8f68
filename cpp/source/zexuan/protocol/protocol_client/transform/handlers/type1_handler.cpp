/**
 * @file type1_handler.cpp
 * @brief Type 1 单帧测试消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/transform/handlers/type1_handler.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace client {

    // 静态常量定义
    const std::string Type1Handler::DEFAULT_TEST_DATA = "SINGLE_FRAME_TEST_DATA";

    Type1Handler::Type1Handler() 
        : SingleTypeMessageHandler("Type1Handler", 1) {
        spdlog::debug("Type1Handler: Created for single frame test messages");
    }

    bool Type1Handler::ValidateTypeSpecificRequest(const MessageRequest& request) const {
        // Type 1 消息特定的验证逻辑

        // 检查目标地址是否合理（通常应该是tcp_bus_client的地址9）
        if (request.target == 0) {
            spdlog::debug("Type1Handler: Target address should not be 0");
            return false;
        }

        // VSQ应该是单信息类型
        if (request.vsq != DEFAULT_VSQ && request.vsq != 0x01) {
            spdlog::debug("Type1Handler: VSQ should be single info type, got {:02X}", request.vsq);
            // 这不是错误，只是警告
        }

        // 如果没有提供测试数据，将使用默认数据
        if (request.text_content.empty() && request.variable_data.empty()) {
            spdlog::debug("Type1Handler: No test data provided, will use default");
        }

        return true;
    }

    base::Result<base::Message> Type1Handler::CreateTypeSpecificMessage(const MessageRequest& request) const {
        spdlog::debug("Type1Handler: Creating Type 1 single frame test message");

        // 创建消息对象
        base::Message message;

        // 使用请求参数，如果未指定则使用默认值
        MessageRequest effective_request = request;
        
        // 设置Type 1的默认值
        if (effective_request.vsq == 0x00) {
            effective_request.vsq = DEFAULT_VSQ;
        }
        if (effective_request.cot == 0x00) {
            effective_request.cot = DEFAULT_COT;
        }
        if (effective_request.fun == 0x00) {
            effective_request.fun = DEFAULT_FUN;
        }
        if (effective_request.inf == 0x00) {
            effective_request.inf = DEFAULT_INF;
        }

        // 设置测试数据
        if (effective_request.text_content.empty() && effective_request.variable_data.empty()) {
            effective_request.text_content = DEFAULT_TEST_DATA;
        }

        // 设置消息字段
        SetBasicMessageFields(message, 1, effective_request);

        spdlog::info("Type1Handler: Created single frame test message to target {}", request.target);
        spdlog::debug("Type1Handler: Message details - VSQ={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
                     effective_request.vsq, effective_request.cot, 
                     effective_request.fun, effective_request.inf);

        return message;
    }

    base::Result<MessageResult> Type1Handler::ProcessTypeSpecificResponse(const base::Message& response) const {
        spdlog::debug("Type1Handler: Processing Type 1 response");

        uint8_t response_type = response.getTyp();
        uint8_t response_cot = response.getCot();
        
        // 打印响应详情
        spdlog::info("Type1Handler: Received response - TYP={:02X}, VSQ={:02X}, COT={:02X}, "
                    "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                    response.getTyp(), response.getVsq(), response.getCot(),
                    response.getSource(), response.getTarget(), response.getFun(), response.getInf());

        // 提取响应内容
        std::string response_content = ExtractTextContent(response);
        std::vector<uint8_t> response_data = ExtractVariableData(response);

        MessageResult result;
        result.success = true;

        // 分析响应类型和传送原因
        if (response_type == 1) {
            // Type 1 响应
            if (response_cot == 0x07) { // 激活确认
                result.description = "Single frame test confirmed";
                spdlog::info("Type1Handler: Single frame test confirmed by server");
            } else if (response_cot == 0x08) { // 停止激活确认
                result.description = "Single frame test stopped";
                spdlog::info("Type1Handler: Single frame test stopped by server");
            } else {
                result.description = "Single frame test response with COT " + std::to_string(response_cot);
                spdlog::info("Type1Handler: Single frame test response with COT {:02X}", response_cot);
            }
        } else {
            // 其他类型的响应
            result.description = "Unexpected response type " + std::to_string(response_type);
            spdlog::warn("Type1Handler: Unexpected response type {} for Type 1 request", response_type);
        }

        // 如果有文本内容，添加到结果中
        if (!response_content.empty()) {
            result.description += " - Content: " + response_content;
            spdlog::debug("Type1Handler: Response content: {}", response_content);
        }

        // 如果有可变结构体数据，保存到结果中
        if (!response_data.empty()) {
            result.additional_data = response_data;
            spdlog::debug("Type1Handler: Response has {} bytes of variable data", response_data.size());
        }

        return result;
    }

} // namespace client
} // namespace protocol
} // namespace zexuan
