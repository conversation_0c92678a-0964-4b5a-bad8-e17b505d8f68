/**
 * @file client_types.hpp
 * @brief 客户端通用类型定义和错误处理
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_TYPES_HPP
#define ZEXUAN_PROTOCOL_CLIENT_TYPES_HPP

#include <string>
#include <system_error>
#include "zexuan/base/base_types.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief 客户端错误代码枚举
     */
    enum class ClientErrorCode : int {
        SUCCESS = 0,                    ///< 成功
        
        // 配置相关错误 (1-20)
        CONFIG_FILE_NOT_FOUND = 1,      ///< 配置文件未找到
        CONFIG_PARSE_ERROR = 2,         ///< 配置文件解析错误  
        CONFIG_VALIDATION_ERROR = 3,    ///< 配置验证失败
        CONFIG_MISSING_REQUIRED = 4,    ///< 缺少必需配置项

        // 连接相关错误 (21-40)
        CONNECTION_FAILED = 21,         ///< 连接失败
        CONNECTION_TIMEOUT = 22,        ///< 连接超时
        CONNECTION_LOST = 23,           ///< 连接丢失
        CONNECTION_REFUSED = 24,        ///< 连接被拒绝
        ALREADY_CONNECTED = 25,         ///< 已经连接
        NOT_CONNECTED = 26,             ///< 未连接

        // 消息相关错误 (41-60)
        MESSAGE_TYPE_NOT_SUPPORTED = 41,///< 消息类型不支持
        MESSAGE_CREATION_FAILED = 42,   ///< 消息创建失败
        MESSAGE_SEND_FAILED = 43,       ///< 消息发送失败
        MESSAGE_PARSE_FAILED = 44,      ///< 消息解析失败
        MESSAGE_VALIDATION_FAILED = 45, ///< 消息验证失败
        MESSAGE_TIMEOUT = 46,           ///< 消息超时

        // 处理器相关错误 (61-80)
        HANDLER_NOT_FOUND = 61,         ///< 处理器未找到
        HANDLER_REGISTER_FAILED = 62,   ///< 处理器注册失败
        HANDLER_ALREADY_EXISTS = 63,    ///< 处理器已存在
        HANDLER_EXECUTION_FAILED = 64,  ///< 处理器执行失败

        // 系统相关错误 (81-100)
        COMPONENT_NOT_INITIALIZED = 81, ///< 组件未初始化
        COMPONENT_START_FAILED = 82,    ///< 组件启动失败
        COMPONENT_STOP_FAILED = 83,     ///< 组件停止失败
        RESOURCE_ALLOCATION_FAILED = 84,///< 资源分配失败
        THREAD_CREATION_FAILED = 85,    ///< 线程创建失败

        // 通用错误 (101-120)
        INVALID_PARAMETER = 101,        ///< 无效参数
        OPERATION_NOT_SUPPORTED = 102,  ///< 操作不支持
        INTERNAL_ERROR = 103,           ///< 内部错误
        UNKNOWN_ERROR = 104             ///< 未知错误
    };

    /**
     * @brief 将错误代码转换为字符串描述
     * @param error_code 错误代码
     * @return 错误描述字符串
     */
    std::string ToString(ClientErrorCode error_code);

    /**
     * @brief 客户端结果类型别名
     * 直接使用base模块的Result类型，但错误码使用ClientErrorCode
     * 注意：这里我们仍然使用base::ErrorCode作为错误类型来保持与现有架构的兼容性
     */
    template<typename T>
    using ClientResult = base::Result<T>;

    /**
     * @brief 创建成功结果
     * @param value 结果值  
     * @return 成功的Result对象
     */
    template<typename T>
    ClientResult<T> MakeSuccess(T&& value) {
        return ClientResult<T>(std::forward<T>(value));
    }

    /**
     * @brief 创建成功结果（void特化）
     * @return 成功的Result对象
     */
    inline base::VoidResult MakeSuccess() {
        return base::VoidResult();
    }

    /**
     * @brief 创建错误结果
     * @param error_code 客户端错误代码（转换为base::ErrorCode）
     * @return 错误的Result对象
     */
    template<typename T>
    ClientResult<T> MakeError(ClientErrorCode error_code) {
        // 将ClientErrorCode映射到base::ErrorCode
        base::ErrorCode base_error = static_cast<base::ErrorCode>(error_code);
        return ClientResult<T>(std::unexpected(base_error));
    }

    /**
     * @brief 创建错误结果（void特化）
     * @param error_code 客户端错误代码
     * @return 错误的Result对象
     */
    inline base::VoidResult MakeError(ClientErrorCode error_code) {
        base::ErrorCode base_error = static_cast<base::ErrorCode>(error_code);
        return base::VoidResult(std::unexpected(base_error));
    }

    /**
     * @brief 日志管理器接口
     * 简化的日志管理器，用于客户端组件
     */
    class IClientLogger {
    public:
        virtual ~IClientLogger() = default;

        /**
         * @brief 初始化日志系统
         * @param component_name 组件名称（用于日志标识）
         * @param config_file_path 配置文件路径
         * @return 初始化结果
         */
        virtual bool Initialize(const std::string& component_name, 
                              const std::string& config_file_path) = 0;

        /**
         * @brief 获取组件名称
         * @return 组件名称
         */
        virtual std::string GetComponentName() const = 0;
    };

    /**
     * @brief 默认日志管理器实现
     * 基于LoggerManager的简单封装
     */
    class DefaultClientLogger : public IClientLogger {
    public:
        DefaultClientLogger() = default;
        ~DefaultClientLogger() override = default;

        bool Initialize(const std::string& component_name, 
                       const std::string& config_file_path) override;
        
        std::string GetComponentName() const override { return component_name_; }

    private:
        std::string component_name_;
        bool initialized_ = false;
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_TYPES_HPP
