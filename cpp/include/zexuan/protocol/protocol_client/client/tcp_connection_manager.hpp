/**
 * @file tcp_connection_manager.hpp
 * @brief TCP连接管理器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_TCP_CONNECTION_MANAGER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_TCP_CONNECTION_MANAGER_HPP

#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <thread>

#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/event_loop_thread.hpp"
#include "zexuan/event/net/tcp_client.hpp"
#include "zexuan/event/net/tcp_connection.hpp"
#include "zexuan/protocol/protocol_client/client/protocol_client.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief 连接事件回调函数类型
     * @param state 新的连接状态
     * @param description 状态描述
     */
    using ConnectionEventCallback = std::function<void(ConnectionState state, const std::string& description)>;

    /**
     * @brief 数据接收回调函数类型  
     * @param data 接收到的数据
     * @param size 数据大小
     */
    using DataReceivedCallback = std::function<void(const uint8_t* data, size_t size)>;

    /**
     * @brief TCP连接管理器
     * 
     * 负责TCP连接的建立、维护、重连和数据收发
     * 封装现有的TcpClient功能，提供更高级的接口
     */
    class TcpConnectionManager {
    public:
        /**
         * @brief 构造函数
         * @param config 客户端配置
         */
        explicit TcpConnectionManager(const ClientConfig& config);

        /**
         * @brief 析构函数
         */
        ~TcpConnectionManager();

        /**
         * @brief 启动连接管理器
         * @return 启动结果
         */
        bool Start();

        /**
         * @brief 停止连接管理器
         * @return 停止结果
         */
        bool Stop();

        /**
         * @brief 是否正在运行
         * @return true: 运行中, false: 已停止
         */
        bool IsRunning() const;

        /**
         * @brief 发起连接
         * @return 连接结果
         */
        bool Connect();

        /**
         * @brief 断开连接
         */
        void Disconnect();

        /**
         * @brief 检查是否已连接
         * @return true: 已连接, false: 未连接
         */
        bool IsConnected() const;

        /**
         * @brief 获取当前连接状态
         * @return 连接状态
         */
        ConnectionState GetConnectionState() const;

        /**
         * @brief 发送数据
         * @param data 要发送的数据
         * @param size 数据大小
         * @return 发送结果
         */
        bool SendData(const uint8_t* data, size_t size);

        /**
         * @brief 发送字符串数据
         * @param message 要发送的消息
         * @return 发送结果
         */
        bool SendMessage(const std::string& message);

        /**
         * @brief 设置连接事件回调
         * @param callback 回调函数
         */
        void SetConnectionEventCallback(ConnectionEventCallback callback);

        /**
         * @brief 设置数据接收回调
         * @param callback 回调函数
         */
        void SetDataReceivedCallback(DataReceivedCallback callback);

        /**
         * @brief 获取连接统计信息
         */
        struct Statistics {
            uint64_t bytes_sent = 0;
            uint64_t bytes_received = 0;
            uint32_t connection_attempts = 0;
            uint32_t reconnection_count = 0;
        };
        Statistics GetStatistics() const;

    private:
        // 配置信息
        ClientConfig config_;

        // 网络组件
        std::unique_ptr<event::EventLoopThread> event_loop_thread_;
        event::EventLoop* event_loop_;
        std::unique_ptr<event::TcpClient> tcp_client_;

        // 状态管理
        std::atomic<bool> is_running_{false};
        std::atomic<ConnectionState> connection_state_{ConnectionState::DISCONNECTED};

        // 统计信息
        mutable std::mutex stats_mutex_;
        Statistics statistics_;

        // 回调函数
        std::mutex callback_mutex_;
        ConnectionEventCallback connection_event_callback_;
        DataReceivedCallback data_received_callback_;

        // 自动重连
        std::atomic<bool> auto_reconnect_enabled_{true};
        std::thread reconnect_thread_;
        std::atomic<bool> should_stop_reconnect_{false};

        // 私有方法
        void OnConnection(const event::TcpConnectionPtr& conn);
        void OnMessage(const event::TcpConnectionPtr& conn, 
                      event::Buffer* buf, 
                      std::chrono::system_clock::time_point receive_time);
        void UpdateConnectionState(ConnectionState new_state, const std::string& description = "");
        void AutoReconnectLoop();
        void UpdateStatistics(const std::function<void(Statistics&)>& updater);
        void SetupSignalHandling();
        
        // 弱指针，用于回调中安全访问
        event::TcpConnectionPtr connection_;
        std::mutex connection_mutex_;
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_TCP_CONNECTION_MANAGER_HPP
