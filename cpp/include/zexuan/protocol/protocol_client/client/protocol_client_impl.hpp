/**
 * @file protocol_client_impl.hpp
 * @brief IEC 60870-5-103 协议客户端实现类
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_PROTOCOL_CLIENT_IMPL_HPP
#define ZEXUAN_PROTOCOL_CLIENT_PROTOCOL_CLIENT_IMPL_HPP

#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

#include "zexuan/protocol/protocol_client/client/protocol_client.hpp"
#include "zexuan/protocol/protocol_client/client/client_types.hpp"

#include "zexuan/base/lifecycle_component.hpp"
#include "zexuan/event/net/tcp_client.hpp"
#include "zexuan/event/event_loop.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    // 前向声明
    class TcpConnectionManager;
    class MessageFrameParser;
    class MessageTypeHandlerFactory;

    /**
     * @brief IEC 60870-5-103 协议客户端实现
     * 
     * 现代化协议客户端实现
     * 支持Type 1-255消息类型的可扩展处理
     */
    class ProtocolClientImpl : public IProtocolClient, 
                              public base::LifecycleComponentBase,
                              public IClientLogger {
    public:
        /**
         * @brief 构造函数
         * @param config_file_path 配置文件路径
         */
        explicit ProtocolClientImpl(const std::string& config_file_path);

        /**
         * @brief 析构函数
         */
        ~ProtocolClientImpl() override;

        // 实现IProtocolClient接口
        bool Start() override;
        bool Stop() override;
        bool IsRunning() const override;
        bool IsConnected() const override;
        ConnectionState GetConnectionState() const override;
        bool Connect() override;
        void Disconnect() override;
        
        base::Result<void> SendMessage(uint8_t message_type, const MessageRequest& request) override;
        base::Result<void> SendRawMessage(const std::string& message) override;
        
        bool RegisterMessageHandler(std::unique_ptr<IMessageTypeHandler> handler) override;
        bool UnregisterMessageHandler(uint8_t message_type) override;
        bool SupportMessageType(uint8_t message_type) const override;
        std::vector<uint8_t> GetSupportedMessageTypes() const override;
        
        void SetConnectionStateCallback(ConnectionStateCallback callback) override;
        void SetMessageResponseCallback(MessageResponseCallback callback) override;
        void SetRawDataReceivedCallback(RawDataReceivedCallback callback) override;
        
        const ClientConfig& GetConfig() const override;
        Statistics GetStatistics() const override;

        // 实现IClientLogger接口
        bool Initialize(const std::string& component_name, 
                       const std::string& config_file_path) override;
        std::string GetComponentName() const override;

        // 实现LifecycleComponentBase接口
        // Start()和Stop()已在IProtocolClient中定义

    private:
        // 配置和初始化
        std::string config_file_path_;
        ClientConfig config_;
        std::unique_ptr<DefaultClientLogger> logger_;

        // 核心组件
        // TCP连接和消息处理组件
        std::unique_ptr<TcpConnectionManager> connection_manager_;
        std::unique_ptr<MessageFrameParser> frame_parser_;
        std::unique_ptr<MessageTypeHandlerFactory> handler_factory_;

        // 状态管理
        std::atomic<bool> is_running_{false};
        std::atomic<ConnectionState> connection_state_{ConnectionState::DISCONNECTED};
        
        // 统计信息
        mutable std::mutex stats_mutex_;
        Statistics statistics_;

        // 回调函数
        std::mutex callback_mutex_;
        ConnectionStateCallback connection_state_callback_;
        MessageResponseCallback message_response_callback_;
        RawDataReceivedCallback raw_data_received_callback_;

        // 私有方法
        bool LoadConfiguration();
        bool CreateCoreComponents();
        bool InitializeMediatorAndComponents();
        bool RegisterBuiltinHandlers();
        void UpdateConnectionState(ConnectionState new_state, const std::string& description = "");
        void OnConnectionStateChanged(ConnectionState state, const std::string& description);
        void OnMessageResponseReceived(uint8_t message_type, const MessageResult& result);
        void UpdateStatistics(const std::function<void(Statistics&)>& updater);
        
        // 连接相关私有方法
        void SetupConnectionCallbacks();
        void OnTcpConnectionStateChanged(ConnectionState state, const std::string& description);
        void OnTcpDataReceived(const uint8_t* data, size_t size);
        void ProcessReceivedMessage(const base::Message& message);
        void ProcessRawFrame(const std::vector<uint8_t>& frame_data);


    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_PROTOCOL_CLIENT_IMPL_HPP
