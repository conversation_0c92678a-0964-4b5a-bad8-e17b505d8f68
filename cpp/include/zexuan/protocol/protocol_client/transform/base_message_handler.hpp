/**
 * @file base_message_handler.hpp
 * @brief 基础消息处理器抽象类
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_BASE_MESSAGE_HANDLER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_BASE_MESSAGE_HANDLER_HPP

#include <string>
#include <vector>
#include <cstdint>

#include "zexuan/protocol/protocol_client/transform/message_type_handler.hpp"
#include "zexuan/base/message.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief 基础消息处理器抽象类
     * 
     * 为所有消息类型处理器提供通用的实现框架
     * 实现了IMessageTypeHandler接口的通用部分
     */
    class BaseMessageHandler : public IMessageTypeHandler {
    public:
        /**
         * @brief 构造函数
         * @param handler_name 处理器名称
         * @param supported_types 支持的消息类型列表
         */
        BaseMessageHandler(const std::string& handler_name, 
                          const std::vector<uint8_t>& supported_types);

        /**
         * @brief 虚析构函数
         */
        ~BaseMessageHandler() override = default;

        // 实现IMessageTypeHandler接口
        bool CanHandle(uint8_t message_type) const override;
        std::string GetHandlerName() const override;
        std::vector<uint8_t> GetSupportedTypes() const override;
        bool ValidateRequest(uint8_t message_type, const MessageRequest& request) const override;

        // 子类需要实现的纯虚函数
        base::Result<base::Message> CreateMessage(uint8_t message_type, 
                                                 const MessageRequest& request) const override = 0;
        base::Result<MessageResult> ProcessResponse(const base::Message& response) const override = 0;

    protected:
        /**
         * @brief 验证基本消息参数
         * @param request 消息请求参数
         * @return 验证结果
         */
        virtual bool ValidateBasicRequest(const MessageRequest& request) const;

        /**
         * @brief 设置消息的基本字段
         * @param message 消息对象
         * @param message_type 消息类型
         * @param request 请求参数
         */
        virtual void SetBasicMessageFields(base::Message& message, 
                                         uint8_t message_type,
                                         const MessageRequest& request) const;

        /**
         * @brief 创建基本的消息结果
         * @param success 是否成功
         * @param description 描述信息
         * @return 消息结果
         */
        virtual MessageResult CreateBasicResult(bool success, const std::string& description = "") const;

        /**
         * @brief 从消息中提取文本内容（如果可能）
         * @param message 消息对象
         * @return 文本内容
         */
        virtual std::string ExtractTextContent(const base::Message& message) const;

        /**
         * @brief 从消息中提取可变结构体数据
         * @param message 消息对象
         * @return 可变结构体数据
         */
        virtual std::vector<uint8_t> ExtractVariableData(const base::Message& message) const;

        /**
         * @brief 验证消息类型是否被支持
         * @param message_type 消息类型
         * @return 验证结果
         */
        bool IsMessageTypeSupported(uint8_t message_type) const;

    private:
        std::string handler_name_;              ///< 处理器名称
        std::vector<uint8_t> supported_types_; ///< 支持的消息类型
    };

    /**
     * @brief 单一消息类型处理器基类
     * 
     * 用于只处理单一消息类型的处理器
     * 简化了单类型处理器的实现
     */
    class SingleTypeMessageHandler : public BaseMessageHandler {
    public:
        /**
         * @brief 构造函数
         * @param handler_name 处理器名称
         * @param message_type 支持的消息类型
         */
        SingleTypeMessageHandler(const std::string& handler_name, uint8_t message_type);

        /**
         * @brief 获取支持的消息类型（单一类型）
         * @return 消息类型
         */
        uint8_t GetMessageType() const { return message_type_; }

    protected:
        /**
         * @brief 验证请求（单一类型的简化版本）
         * @param request 消息请求参数
         * @return 验证结果
         */
        virtual bool ValidateTypeSpecificRequest(const MessageRequest& request) const = 0;

        /**
         * @brief 创建特定类型的消息（子类实现）
         * @param request 请求参数
         * @return 创建的消息
         */
        virtual base::Result<base::Message> CreateTypeSpecificMessage(const MessageRequest& request) const = 0;

        /**
         * @brief 处理特定类型的响应（子类实现）
         * @param response 响应消息
         * @return 处理结果
         */
        virtual base::Result<MessageResult> ProcessTypeSpecificResponse(const base::Message& response) const = 0;

        // 实现BaseMessageHandler的纯虚函数
        bool ValidateRequest(uint8_t message_type, const MessageRequest& request) const override final;
        base::Result<base::Message> CreateMessage(uint8_t message_type, 
                                                 const MessageRequest& request) const override final;
        base::Result<MessageResult> ProcessResponse(const base::Message& response) const override final;

    private:
        uint8_t message_type_; ///< 支持的单一消息类型
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_BASE_MESSAGE_HANDLER_HPP
