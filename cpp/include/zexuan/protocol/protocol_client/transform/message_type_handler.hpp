/**
 * @file message_type_handler.hpp
 * @brief 消息类型处理器接口定义
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_MESSAGE_TYPE_HANDLER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_MESSAGE_TYPE_HANDLER_HPP

#include <cstdint>
#include <memory>
#include <string>
#include <vector>

#include "zexuan/base/message.hpp"
#include "zexuan/base/base_types.hpp"

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief 消息请求参数结构
     */
    struct MessageRequest {
        uint8_t vsq = 0x81;        ///< 可变结构限定词
        uint8_t cot = 0x06;        ///< 传送原因  
        uint8_t source = 0x01;     ///< 源地址
        uint8_t target = 0x09;     ///< 目标地址
        uint8_t fun = 0xFF;        ///< 功能类型
        uint8_t inf = 0x01;        ///< 信息序号
        std::string text_content;  ///< 文本内容（如果有）
        std::vector<uint8_t> variable_data; ///< 可变结构体数据（如果有）
        
        MessageRequest() = default;
        MessageRequest(uint8_t target_addr, const std::string& content = "")
            : target(target_addr), text_content(content) {}
    };

    /**
     * @brief 消息处理结果
     */
    struct MessageResult {
        bool success = false;
        std::string description;
        std::vector<uint8_t> additional_data; ///< 额外的处理数据
        
        MessageResult() = default;
        MessageResult(bool ok, const std::string& desc = "") 
            : success(ok), description(desc) {}
    };

    /**
     * @brief 消息类型处理器接口
     * 
     * 每种消息类型都需要实现此接口来处理：
     * 1. 消息的构造和发送
     * 2. 响应消息的解析和处理  
     * 3. 类型识别和验证
     */
    class IMessageTypeHandler {
    public:
        virtual ~IMessageTypeHandler() = default;

        /**
         * @brief 检查是否能处理指定消息类型
         * @param message_type 消息类型 (TYP字段)
         * @return true:能处理 false:不能处理
         */
        virtual bool CanHandle(uint8_t message_type) const = 0;

        /**
         * @brief 创建指定类型的消息
         * @param message_type 消息类型
         * @param request 消息请求参数
         * @return 创建的消息对象，失败返回错误
         */
        virtual base::Result<base::Message> CreateMessage(uint8_t message_type, 
                                                          const MessageRequest& request) const = 0;

        /**
         * @brief 处理响应消息
         * @param response 收到的响应消息
         * @return 处理结果
         */
        virtual base::Result<MessageResult> ProcessResponse(const base::Message& response) const = 0;

        /**
         * @brief 获取处理器名称（用于日志和调试）
         * @return 处理器名称
         */
        virtual std::string GetHandlerName() const = 0;

        /**
         * @brief 获取支持的消息类型列表
         * @return 支持的消息类型数组
         */
        virtual std::vector<uint8_t> GetSupportedTypes() const = 0;

        /**
         * @brief 验证消息请求参数的有效性
         * @param message_type 消息类型
         * @param request 请求参数
         * @return 验证结果
         */
        virtual bool ValidateRequest(uint8_t message_type, const MessageRequest& request) const = 0;
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_MESSAGE_TYPE_HANDLER_HPP
