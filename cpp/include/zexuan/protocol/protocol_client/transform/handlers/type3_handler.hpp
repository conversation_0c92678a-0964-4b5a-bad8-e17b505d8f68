/**
 * @file type3_handler.hpp
 * @brief Type 3 时间戳事件消息处理器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE3_HANDLER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE3_HANDLER_HPP

#include "zexuan/protocol/protocol_client/transform/base_message_handler.hpp"
#include <chrono>

namespace zexuan {
namespace protocol {
namespace client {

    /**
     * @brief Type 3 时间戳事件消息处理器
     * 
     * 处理IEC 60870-5-103协议中的Type 3（时间戳事件）消息
     * 对应原始ChatClient中的parseType3TimestampEvent功能
     */
    class Type3Handler : public SingleTypeMessageHandler {
    public:
        /**
         * @brief 构造函数
         */
        Type3Handler();

        /**
         * @brief 析构函数
         */
        ~Type3Handler() override = default;

    protected:
        // 实现SingleTypeMessageHandler的纯虚函数
        bool ValidateTypeSpecificRequest(const MessageRequest& request) const override;
        base::Result<base::Message> CreateTypeSpecificMessage(const MessageRequest& request) const override;
        base::Result<MessageResult> ProcessTypeSpecificResponse(const base::Message& response) const override;

    private:
        // Type 3 消息的默认参数
        static constexpr uint8_t DEFAULT_VSQ = 0x81;    ///< 默认可变结构限定词
        static constexpr uint8_t DEFAULT_COT = 0x01;    ///< 默认传送原因（周期、循环）
        static constexpr uint8_t DEFAULT_FUN = 0xFF;    ///< 默认功能类型
        static constexpr uint8_t DEFAULT_INF = 0x01;    ///< 默认信息序号（事件信息）

        // IEC 60870-5-103 时间格式相关常量
        static constexpr size_t IEC103_TIMESTAMP_SIZE = 7; ///< IEC103时间戳大小（7字节）

        /**
         * @brief 解析IEC 60870-5-103时间戳
         * @param timestamp_data 7字节的时间戳数据
         * @return 解析后的时间点和描述
         */
        std::pair<std::chrono::system_clock::time_point, std::string> ParseIEC103Timestamp(
            const std::vector<uint8_t>& timestamp_data) const;

        /**
         * @brief 创建当前时间的IEC 60870-5-103时间戳
         * @return 7字节的时间戳数据
         */
        std::vector<uint8_t> CreateCurrentIEC103Timestamp() const;

        /**
         * @brief 格式化时间戳为可读字符串
         * @param year 年份（相对于2000年）
         * @param month 月份
         * @param day 日期
         * @param hour 小时
         * @param minute 分钟
         * @param milliseconds 毫秒
         * @return 格式化的时间字符串
         */
        std::string FormatTimestamp(uint8_t year, uint8_t month, uint8_t day, 
                                   uint8_t hour, uint8_t minute, uint16_t milliseconds) const;
    };

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE3_HANDLER_HPP
